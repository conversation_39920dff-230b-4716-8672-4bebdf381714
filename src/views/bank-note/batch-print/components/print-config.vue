<template>
  <div class="print-config">
    <el-card shadow="never">
      <template #header>
        <span>打印配置</span>
      </template>

      <div class="config-content">
        <el-row :gutter="20">
          <!-- 自定义模板选择 -->
          <el-col :span="8">
            <div class="config-item">
              <label class="config-label">自定义模板：</label>
              <el-select
                v-model="config.templateId"
                placeholder="请选择自定义模板"
                style="width: 200px"
                @change="handleTemplateChange"
              >
                <el-option
                  v-for="template in customTemplateOptions"
                  :key="template.id"
                  :label="template.templateName || template.name"
                  :value="template.id"
                >
                  <span>{{ template.templateName || template.name }}</span>
                  <el-tag
                    v-if="template.isDefault"
                    type="success"
                    size="small"
                    style="margin-left: 8px"
                  >
                    默认
                  </el-tag>
                </el-option>
              </el-select>
            </div>
          </el-col>

          <!-- 转换类型选择 -->
          <el-col :span="6">
            <div class="config-item">
              <label class="config-label">转换类型：</label>
              <el-select
                v-model="config.conversionType"
                placeholder="请选择转换类型"
                style="width: 150px"
              >
                <el-option
                  v-for="item in conversionOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-col>

          <!-- 操作按钮 -->
          <el-col :span="4">
            <div class="config-buttons">
              <el-button
                type="info"
                :icon="DocumentChecked"
                @click="handleAuditCheck"
                :loading="auditChecking"
              >
                审核检测
              </el-button>
              <el-button
                type="success"
                :icon="Printer"
                @click="handlePrintLabel"
                :disabled="!canPrint"
                :loading="printing"
              >
                打印标签
              </el-button>
              <el-button
                v-if="config.templateType === 'CUSTOM' && config.templateId"
                type="primary"
                @click="previewTemplate"
                :loading="previewing"
              >
                预览模板
              </el-button>
              <!--              <el-button
                :icon="Document"
                @click="handlePrintWhite"
                :loading="printingWhite"
              >
                白标打印
              </el-button>-->
            </div>
          </el-col>
        </el-row>

        <!-- 选择统计信息 -->
        <div class="selection-info">
          <el-alert
            :title="`已选择 ${selectedCount} 条记录${selectedCount === 0 ? '，不选中默认打印当前页面所有记录' : ''}`"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DocumentChecked, Printer, Document } from '@element-plus/icons-vue';
  import {
    checkCoinAuditStatus,
    getTemplateList
  } from '@/views/bank-note/batch-print/api/index.js';
  import { getTemplateList as getCustomTemplateList } from '@/views/bank-note/label-hiprint/api/index.js';

  const props = defineProps({
    selectedCoins: {
      type: Array,
      default: () => []
    },
    allCoins: {
      type: Array,
      default: () => []
    }
  });

  const emit = defineEmits(['print-label', 'print-white', 'audit-check']);

  // 响应式数据
  const auditChecking = ref(false);
  const printing = ref(false);
  const printingWhite = ref(false);
  const previewing = ref(false);
  const canPrint = ref(false);

  // 配置数据
  const config = reactive({
    templateId: null,
    conversionType: 0
  });

  // 选项数据
  const customTemplateOptions = ref([]);
  const conversionOptions = ref([
    { id: 1, name: '默认', value: 0 },
    { id: 2, name: '繁体（纸币除外）', value: 1 }
  ]);

  // 计算属性
  const selectedCount = computed(() => props.selectedCoins.length);

  // 处理模板变化
  const handleTemplateChange = () => {
    canPrint.value = false; // 重新选择模板后需要重新审核检测
  };

  // 打开模板设计器
  const openTemplateDesigner = () => {
    // 在新窗口中打开模板设计器
    const routeUrl = '/bank-note/label-hiprint';
    window.open(routeUrl, '_blank');
  };

  // 预览模板
  const previewTemplate = async () => {
    if (!config.templateId) {
      EleMessage.error('请先选择模板');
      return;
    }

    previewing.value = true;
    try {
      // 这里可以调用预览API
      EleMessage.success('预览功能开发中...');
    } catch (error) {
      EleMessage.error('预览失败：' + error.message);
    } finally {
      previewing.value = false;
    }
  };

  // 处理审核检测
  const handleAuditCheck = async () => {
    if (!config.templateId) {
      EleMessage.error('请先选择自定义模板');
      return;
    }

    const targetCoins =
      props.selectedCoins.length > 0 ? props.selectedCoins : props.allCoins;

    if (targetCoins.length === 0) {
      EleMessage.warning('没有可检测的钱币记录');
      return;
    }

    auditChecking.value = true;

    try {
      const coinIds = targetCoins.map((coin) => coin.id);
      const result = await checkCoinAuditStatus(coinIds);

      if (result.unauditedCount > 0) {
        const unauditedNumbers = result.unauditedSendforms || [];
        EleMessage.error(`送评单号：${unauditedNumbers.join('、')} 未审核！`);
        canPrint.value = false;
      } else {
        EleMessage.success('审核通过');
        canPrint.value = true;
      }

      emit('audit-check', result);
    } catch (error) {
      EleMessage.error('审核检测失败：' + error.message);
      canPrint.value = false;
    } finally {
      auditChecking.value = false;
    }
  };

  // 处理标签打印
  const handlePrintLabel = async () => {
    if (!canPrint.value) {
      EleMessage.error('请先进行审核检测');
      return;
    }

    const targetCoins =
      props.selectedCoins.length > 0 ? props.selectedCoins : props.allCoins;

    if (targetCoins.length === 0) {
      EleMessage.warning('没有可打印的钱币记录');
      return;
    }

    // 如果没有选中记录，确认是否打印当前页面
    if (props.selectedCoins.length === 0) {
      try {
        await ElMessageBox.confirm(
          '不选中默认打印当前页面，是否继续？',
          '确认打印',
          { type: 'warning' }
        );
      } catch {
        return; // 用户取消
      }
    }

    printing.value = true;

    try {
      const coinIds = targetCoins.map((coin) => coin.id);

      const printParams = {
        coinIds: coinIds,
        customTemplateId: config.templateId,
        conversionType: config.conversionType
      };

      emit('print-label', printParams);
    } catch (error) {
      EleMessage.error('打印失败：' + error.message);
    } finally {
      printing.value = false;
    }
  };

  // 处理白标打印
  const handlePrintWhite = async () => {
    const targetCoins =
      props.selectedCoins.length > 0 ? props.selectedCoins : props.allCoins;

    if (targetCoins.length === 0) {
      EleMessage.warning('没有可打印的钱币记录');
      return;
    }

    // 如果没有选中记录，确认是否打印当前页面
    if (props.selectedCoins.length === 0) {
      try {
        await ElMessageBox.confirm(
          '不选中默认打印当前页面，是否继续？',
          '确认打印',
          { type: 'warning' }
        );
      } catch {
        return; // 用户取消
      }
    }

    printingWhite.value = true;

    try {
      const printParams = {
        coinIds: targetCoins.map((coin) => coin.id),
        customTemplateId: config.templateId, // 白标打印也使用自定义模板
        conversionType: config.conversionType
      };

      emit('print-white', printParams);
    } catch (error) {
      EleMessage.error('白标打印失败：' + error.message);
    } finally {
      printingWhite.value = false;
    }
  };

  // 加载自定义模板列表
  const loadCustomTemplates = async () => {
    try {
      const templates = await getCustomTemplateList();
      customTemplateOptions.value = templates || [];

      // 如果有默认模板，自动选择
      const defaultTemplate = templates.find((t) => t.isDefault);
      if (defaultTemplate) {
        config.templateId = defaultTemplate.id;
      }
    } catch (error) {
      console.error('加载自定义模板列表失败:', error);
      EleMessage.error('加载自定义模板列表失败：' + error.message);
    }
  };

  // 组件挂载时加载数据
  onMounted(() => {
    loadCustomTemplates();
  });

  // 暴露方法给父组件
  defineExpose({
    getConfig: () => config,
    resetCanPrint: () => {
      canPrint.value = false;
    }
  });
</script>

<style scoped>
  .print-config {
    margin-bottom: 16px;
  }

  .config-content {
    padding: 8px 0;
  }

  .config-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .config-label {
    display: inline-block;
    width: 80px;
    font-weight: 500;
    color: #606266;
  }

  .config-buttons {
    display: flex;
    gap: 12px;
  }

  .print-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 12px 0;
    border-top: 1px solid #eee;
  }

  .selection-info {
    margin-top: 16px;
  }

  :deep(.el-alert) {
    border-radius: 4px;
  }
</style>
