/**
 * hiprint 打印预览与实际打印一致性修复方案
 * 
 * 问题：打印预览显示一页多签横向布局，但实际打印变成一页一签纵向布局
 * 
 * 解决方案：
 * 1. 统一预览和打印的纸张配置
 * 2. 正确设置批量打印参数
 * 3. 确保多标签布局配置一致
 */

// 1. 获取模板纸张信息的增强函数
export function getTemplatePaperInfo(templateConfig) {
  if (!templateConfig || !templateConfig.panels || !templateConfig.panels[0]) {
    return {
      width: 210,
      height: 297,
      orientation: 'portrait',
      paperType: 'A4',
      labelsPerRow: 1,
      labelsPerColumn: 1,
      labelSpacing: { horizontal: 0, vertical: 0 },
      margin: { top: 10, right: 10, bottom: 10, left: 10 }
    };
  }

  const panel = templateConfig.panels[0];
  const paperInfo = {
    width: panel.width || 210,
    height: panel.height || 297,
    orientation: panel.width > panel.height ? 'landscape' : 'portrait',
    paperType: 'CUSTOM',
    margin: {
      top: panel.paperNumberTop || 10,
      right: panel.paperNumberLeft || 10,
      bottom: panel.paperNumberTop || 10,
      left: panel.paperNumberLeft || 10
    }
  };

  // 分析模板布局，确定是否为多标签模板
  const elements = panel.printElements || [];
  if (elements.length > 0) {
    // 检查是否有重复的元素位置，判断是否为多标签布局
    const positions = elements.map(el => ({ x: el.options.left, y: el.options.top }));
    const uniquePositions = [...new Set(positions.map(p => `${p.x},${p.y}`))];
    
    if (positions.length > uniquePositions.length) {
      // 有重复位置，可能是多标签模板
      paperInfo.labelsPerRow = 2; // 默认每行2个标签
      paperInfo.labelsPerColumn = 1;
      paperInfo.labelSpacing = { horizontal: 10, vertical: 5 };
    } else {
      paperInfo.labelsPerRow = 1;
      paperInfo.labelsPerColumn = 1;
      paperInfo.labelSpacing = { horizontal: 0, vertical: 0 };
    }
  }

  return paperInfo;
}

// 2. 创建统一的打印配置
export function createUnifiedPrintOptions(templateConfig, printParams) {
  const templatePaperInfo = getTemplatePaperInfo(templateConfig);
  
  console.log('=== 统一打印配置 ===');
  console.log('模板纸张信息:', templatePaperInfo);
  
  const printOptions = {
    // 基础打印设置
    preview: false, // 不显示预览对话框，直接打印
    printer: '', // 使用默认打印机
    title: '批量标签打印',
    
    // 纸张设置 - 使用模板的实际纸张配置
    paperType: templatePaperInfo.paperType,
    width: templatePaperInfo.width, // mm
    height: templatePaperInfo.height, // mm
    
    // 页面方向设置 - 关键：确保与预览一致
    orientation: templatePaperInfo.orientation,
    
    // 页面边距设置
    margin: templatePaperInfo.margin,

    // 批量打印配置
    batchPrint: true, // 启用批量打印
    autoPage: true, // 启用自动分页
    pageBreak: false, // 禁用强制分页，让多标签在同一页显示
    
    // 多标签布局配置
    multiLabel: templatePaperInfo.labelsPerRow > 1 || templatePaperInfo.labelsPerColumn > 1,
    labelsPerRow: templatePaperInfo.labelsPerRow,
    labelsPerColumn: templatePaperInfo.labelsPerColumn,
    labelsPerPage: templatePaperInfo.labelsPerRow * templatePaperInfo.labelsPerColumn,

    // 标签间距设置
    labelSpacing: templatePaperInfo.labelSpacing,

    // 打印质量设置
    scale: 1, // 不缩放，保持原始尺寸
    fit: false, // 不自动适应页面
    shrinkToFit: false, // 不缩小适应
    useActualSize: true, // 使用实际尺寸
    dpi: 300, // 高质量打印DPI

    // 颜色模式设置
    colorMode: 'color', // 支持彩色打印
    quality: 'high', // 高质量打印

    // 打印模式
    mode: templatePaperInfo.labelsPerRow > 1 ? 'batch-labels' : 'single-label',
    
    // 打印份数和双面设置
    copies: 1,
    duplex: 'none', // 单面打印
    
    // 纸张来源
    paperSource: 'auto'
  };

  // 如果有多个数据项，计算分页信息
  if (printParams.items && Array.isArray(printParams.items) && printParams.items.length > 1) {
    const totalLabels = printParams.items.length;
    const labelsPerPage = printOptions.labelsPerPage;
    const totalPages = Math.ceil(totalLabels / labelsPerPage);

    console.log(`=== 批量打印信息 ===`);
    console.log(`总标签数: ${totalLabels}`);
    console.log(`每页标签数: ${labelsPerPage}`);
    console.log(`总页数: ${totalPages}`);
    console.log(`纸张方向: ${printOptions.orientation}`);
    console.log(`纸张尺寸: ${printOptions.width}×${printOptions.height}mm`);

    printOptions.totalPages = totalPages;
    printOptions.totalLabels = totalLabels;
  }

  return printOptions;
}

// 3. 修复预览配置，确保与打印一致
export function createUnifiedPreviewOptions(templateConfig) {
  const templatePaperInfo = getTemplatePaperInfo(templateConfig);
  
  return {
    preview: true,
    width: templatePaperInfo.width + 'mm',
    height: templatePaperInfo.height + 'mm',
    orientation: templatePaperInfo.orientation,
    margin: templatePaperInfo.margin,
    scale: 1,
    // 预览时也要考虑多标签布局
    multiLabel: templatePaperInfo.labelsPerRow > 1,
    labelsPerRow: templatePaperInfo.labelsPerRow,
    labelsPerColumn: templatePaperInfo.labelsPerColumn
  };
}

// 4. 打印执行函数
export async function executeUnifiedPrint(hiprintTemplate, printParams, templateConfig) {
  try {
    // 创建统一的打印配置
    const printOptions = createUnifiedPrintOptions(templateConfig, printParams);
    
    console.log('=== 执行统一打印 ===');
    console.log('打印配置:', printOptions);
    console.log('打印数据数量:', printParams.items?.length || 0);
    
    // 使用hiprint的print2方法进行打印
    hiprintTemplate.print2(printParams.items, printOptions);
    
    return {
      success: true,
      message: '打印任务已发送到客户端',
      config: printOptions
    };
  } catch (error) {
    console.error('统一打印执行失败:', error);
    return {
      success: false,
      message: '打印失败：' + error.message,
      error
    };
  }
}

// 5. 预览执行函数
export async function executeUnifiedPreview(hiprintTemplate, previewData, templateConfig) {
  try {
    // 创建统一的预览配置
    const previewOptions = createUnifiedPreviewOptions(templateConfig);
    
    console.log('=== 执行统一预览 ===');
    console.log('预览配置:', previewOptions);
    
    // 使用相同的配置进行预览渲染
    const htmlContent = hiprintTemplate.getHtml(previewData, previewOptions);
    
    return {
      success: true,
      htmlContent,
      config: previewOptions
    };
  } catch (error) {
    console.error('统一预览执行失败:', error);
    return {
      success: false,
      message: '预览失败：' + error.message,
      error
    };
  }
}

export default {
  getTemplatePaperInfo,
  createUnifiedPrintOptions,
  createUnifiedPreviewOptions,
  executeUnifiedPrint,
  executeUnifiedPreview
};
