<template>
  <div class="font-preview-container">
    <div class="font-preview-header">
      <h4>字体预览</h4>
      <el-input
        v-model="previewText"
        placeholder="输入预览文字"
        size="small"
        style="width: 200px;"
      />
    </div>
    
    <div class="font-list">
      <div
        v-for="font in fontList"
        :key="font.value"
        class="font-item"
        :class="{ active: selectedFont === font.value }"
        @click="selectFont(font)"
      >
        <div class="font-name">{{ font.title }}</div>
        <div 
          class="font-preview"
          :style="{ fontFamily: font.value }"
        >
          {{ previewText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  fontList: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const previewText = ref('中国人民银行 1980年贰角 68 ABC123');
const selectedFont = ref(props.modelValue);

const selectFont = (font) => {
  selectedFont.value = font.value;
  emit('update:modelValue', font.value);
  emit('change', font);
};

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  selectedFont.value = newVal;
});
</script>

<style scoped>
.font-preview-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.font-preview-header {
  padding: 12px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.font-preview-header h4 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.font-list {
  max-height: 300px;
  overflow-y: auto;
}

.font-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.font-item:hover {
  background-color: #f5f5f5;
}

.font-item.active {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.font-name {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.font-preview {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  min-height: 20px;
}

/* 确保字体能正确显示 */
.font-preview {
  font-feature-settings: normal;
  font-variant-ligatures: normal;
}
</style>
