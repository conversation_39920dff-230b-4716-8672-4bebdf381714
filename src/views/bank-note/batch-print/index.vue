<template>
  <div class="batch-print-page">
    <!-- 查询条件 -->
    <SearchComponent
      ref="searchRef"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 打印配置 -->
    <PrintConfig
      ref="printConfigRef"
      :selected-coins="selectedCoins"
      :all-coins="coinList"
      @print-label="handlePrintLabel"
      @print-white="handlePrintWhite"
      @audit-check="handleAuditCheck"
    />

    <!-- 数据表格 -->
    <el-card shadow="never">
      <template #header>
        <div class="table-header">
          <span>钱币列表</span>
          <div class="header-actions">
            <el-button
              :icon="Refresh"
              @click="handleRefresh"
              :loading="loading"
            >
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="coinList"
        border
        stripe
        height="500"
        @selection-change="handleSelectionChange"
      >
        <!-- 选择列 -->
        <el-table-column type="selection" width="50" />

        <!-- 钱币编号 -->
        <el-table-column
          prop="serialNumber"
          label="钱币编号"
          width="120"
          show-overflow-tooltip
        />

        <!-- 送评单号 -->
        <el-table-column
          prop="sendformNumber"
          label="送评单号"
          width="120"
          show-overflow-tooltip
        />

        <!-- 鉴定结果 -->
        <el-table-column
          prop="authenticity"
          label="结果"
          width="100"
          align="center"
        />

        <!-- 钱币名称 -->
        <el-table-column
          prop="coinName"
          label="名称"
          width="150"
          show-overflow-tooltip
        />

        <!-- 版别 -->
        <el-table-column
          prop="version"
          label="版别"
          width="120"
          show-overflow-tooltip
        />

        <!-- 附加信息 -->
        <el-table-column
          prop="additionalInfo"
          label="附加"
          width="120"
          show-overflow-tooltip
        />

        <!-- 年代 -->
        <el-table-column
          prop="yearInfo"
          label="年代"
          width="100"
          show-overflow-tooltip
        />

        <!-- 等级 -->
        <el-table-column
          prop="gradeLevel"
          label="等级"
          width="80"
          align="center"
        />

        <!-- 重量 -->
        <el-table-column prop="weight" label="重量" width="80" align="center" />

        <!-- 尺寸 -->
        <el-table-column
          prop="size"
          label="尺寸"
          width="100"
          show-overflow-tooltip
        />

        <!-- 品相分数 -->
        <el-table-column
          prop="gradeScore"
          label="品相分数"
          width="100"
          align="center"
        />

        <!-- 客户姓名 -->
        <el-table-column
          prop="customerName"
          label="姓名 / 网名"
          width="120"
          show-overflow-tooltip
        />

        <!-- 费用 -->
        <el-table-column prop="fee" label="费用" width="80" align="center" />

        <!-- 审核状态 -->
        <el-table-column
          prop="auditStatus"
          label="审核状态"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="row.auditStatus === 1 ? 'success' : 'warning'">
              {{ row.auditStatus === 1 ? '已审核' : '未审核' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 打印标签预览 -->
    <!--    <PrintLabels
      v-model="showPrintPreview"
      :print-data="printData"
      @confirm-print="handleConfirmPrint"
    />-->

    <!-- 增强预览组件 -->
    <EnhancedPreview
      v-model="showEnhancedPreview"
      :print-data="printData"
      @confirm-print="handleConfirmPrint"
      @refresh="handlePreviewRefresh"
    />
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Refresh } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { ElMessageBox } from 'element-plus/es';

  // 组件导入
  import SearchComponent from './components/search.vue';
  import PrintConfig from './components/print-config.vue';
  import PrintLabels from './components/print-labels.vue';
  import EnhancedPreview from './components/EnhancedPreview.vue';

  // API导入
  import { queryPrintCoins, generatePrintData, executePrint } from './api';

  // 响应式数据
  const searchRef = ref();
  const printConfigRef = ref();
  const tableRef = ref();
  const loading = ref(false);
  const coinList = ref([]);
  const selectedCoins = ref([]);
  const showPrintPreview = ref(false);
  const showEnhancedPreview = ref(false);
  const printData = ref(null);
  const currentLabelType = ref(null);

  // 分页数据
  const pagination = reactive({
    current: 1,
    size: 20,
    total: 0
  });

  // 查询参数
  const searchParams = ref({});

  // 处理查询
  const handleSearch = (params) => {
    searchParams.value = params;
    pagination.current = 1;
    loadCoinList();
  };

  // 处理重置
  const handleReset = () => {
    searchParams.value = {};
    pagination.current = 1;
    loadCoinList();
  };

  // 处理刷新
  const handleRefresh = () => {
    loadCoinList();
  };

  // 处理选择变化
  const handleSelectionChange = (selection) => {
    selectedCoins.value = selection;
    // 重置打印权限
    printConfigRef.value?.resetCanPrint();
  };

  // 处理分页大小变化
  const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.current = 1;
    loadCoinList();
  };

  // 处理当前页变化
  const handleCurrentChange = (current) => {
    pagination.current = current;
    loadCoinList();
  };

  // 加载钱币列表
  const loadCoinList = async () => {
    loading.value = true;

    try {
      const params = {
        ...searchParams.value,
        current: pagination.current,
        size: pagination.size
      };

      const result = await queryPrintCoins(params);
      coinList.value = result.list || [];
      pagination.total = result.count || 0;

      // 清空选择
      selectedCoins.value = [];
      tableRef.value?.clearSelection();
    } catch (error) {
      EleMessage.error('查询失败：' + error.message);
      coinList.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  };

  // 处理审核检测
  const handleAuditCheck = (result) => {
    console.log('审核检测结果:', result);
  };

  // 处理标签打印
  const handlePrintLabel = async (params) => {
    try {
      const data = await generatePrintData(params);
      printData.value = data;

      // 默认使用增强预览
      showEnhancedPreview.value = true;
    } catch (error) {
      EleMessage.error('生成打印数据失败：' + error.message);
    }
  };

  // 处理白标打印
  const handlePrintWhite = async (params) => {
    try {
      const data = await generatePrintData({
        ...params,
        printType: 'white'
      });
      printData.value = data;
      showEnhancedPreview.value = true;
    } catch (error) {
      EleMessage.error('生成白标打印数据失败：' + error.message);
    }
  };

  // 处理确认打印
  const handleConfirmPrint = async (printParams) => {
    try {
      // 检查 hiprnt 客户端连接状态
      if (!window.hiwebSocket || !window.hiwebSocket.opened) {
        // 客户端未连接，显示提示信息
        try {
          await ElMessageBox.confirm(
            '连接打印客户端失败！请确保目标服务器已下载并运行打印服务！是否尝试启动客户端？',
            '客户端未连接',
            {
              confirmButtonText: '启动客户端',
              cancelButtonText: '取消',
              type: 'warning',
              dangerouslyUseHTMLString: true,
              message: `
                <div>
                  <p>连接打印客户端失败！</p>
                </div>
              `
            }
          );
          // 尝试通过 URL Scheme 启动客户端
          window.open('hiprint://', '_blank');
        } catch {
          // 用户取消
        }
        return;
      }

      // 客户端已连接，进行打印
      if (!printParams.layoutConfig || !printParams.items) {
        throw new Error('打印数据不完整');
      }

      // 动态导入 hiprint
      const { hiprint } = await import('vue-plugin-hiprint');

      // 创建 hiprint 模板实例
      const hiprintTemplate = new hiprint.PrintTemplate({
        template: printParams.layoutConfig
      });

      // 解析模板配置，提取打印选项
      const templateConfig = printParams.layoutConfig;
      const pageSettings = printParams.pageSettings; // 从接口获取的页面设置

      const printOptions = {
        printer: '', // 使用默认打印机
        title: `标签打印_${new Date().getTime()}`,
        copies: 1, // 默认打印份数
      };

      // 根据模板配置设置打印选项
      if (templateConfig && templateConfig.panels && templateConfig.panels.length > 0) {
        const panel = templateConfig.panels[0];

        // 单个标签的尺寸
        const labelWidth = panel.width || 191;   // 单个标签宽度(mm)
        const labelHeight = panel.height || 26;  // 单个标签高度(mm)

        // 纸张尺寸（从pageSettings获取）
        let paperWidth = 210;  // A4默认宽度
        let paperHeight = 297; // A4默认高度

        if (pageSettings) {
          paperWidth = pageSettings.paperWidth || 210;
          paperHeight = pageSettings.paperHeight || 297;

          console.log('页面设置:', pageSettings);
          console.log(`纸张尺寸: ${paperWidth}mm × ${paperHeight}mm`);
        }

        // 设置实际纸张尺寸
        printOptions.paperWidth = paperWidth;
        printOptions.paperHeight = paperHeight;

        // 根据纸张和标签尺寸计算每页可打印的标签数量
        const labelsPerRow = Math.floor(paperWidth / labelWidth);
        const labelsPerColumn = Math.floor(paperHeight / labelHeight);
        const labelsPerPage = labelsPerRow * labelsPerColumn;

        console.log(`单个标签尺寸: ${labelWidth}mm × ${labelHeight}mm`);
        console.log(`每行标签数: ${labelsPerRow}`);
        console.log(`每列标签数: ${labelsPerColumn}`);
        console.log(`每页标签数: ${labelsPerPage}`);

        // 设置标签布局
        printOptions.labelWidth = labelWidth;
        printOptions.labelHeight = labelHeight;
        printOptions.labelsPerRow = labelsPerRow;
        printOptions.labelsPerColumn = labelsPerColumn;
        printOptions.labelsPerPage = labelsPerPage;

        // 启用自动分页
        printOptions.autoPage = true;
        printOptions.pageBreak = true; // 启用分页

        // 设置标签间距（可选，默认为0）
        printOptions.labelSpacing = {
          horizontal: 0, // 水平间距
          vertical: 0    // 垂直间距
        };

        // 设置页面边距
        printOptions.margin = {
          top: 0,
          right: 0,
          bottom: 0,
          left: 0
        };

        // 检查是否有彩色元素，决定是否彩色打印
        const hasColorElements = panel.printElements?.some(element => {
          const options = element.options || {};
          return options.color || options.backgroundColor ||
                 options.borderColor || options.fontColor;
        });
        printOptions.color = hasColorElements; // 根据元素是否有颜色设置彩色打印

        // 设置页面布局选项
        if (panel.panelLayoutOptions) {
          const layoutOptions = panel.panelLayoutOptions;
          if (layoutOptions.layoutType === 'column') {
            printOptions.layoutType = 'column';
          }
          if (layoutOptions.layoutRowGap) {
            printOptions.rowGap = layoutOptions.layoutRowGap;
          }
          if (layoutOptions.layoutColumnGap) {
            printOptions.columnGap = layoutOptions.layoutColumnGap;
          }
        }

        // 设置水印选项
        if (panel.watermarkOptions && panel.watermarkOptions.content) {
          printOptions.watermark = {
            content: panel.watermarkOptions.content,
            fillStyle: panel.watermarkOptions.fillStyle || 'rgba(184, 184, 184, 0.3)',
            fontSize: panel.watermarkOptions.fontSize || '14px',
            rotate: panel.watermarkOptions.rotate || 25
          };
        }
      }

      // 添加更多标签打印专用设置
      printOptions.scale = 1; // 不缩放
      printOptions.fit = false; // 不自动适应页面
      printOptions.shrinkToFit = false; // 不缩小适应

      // 设置DPI以确保打印质量
      printOptions.dpi = 300; // 高质量打印

      // 确保使用实际尺寸打印
      printOptions.useActualSize = true;

      // 设置打印模式为多标签模式
      printOptions.mode = 'multiple-labels'; // 多标签打印模式

      // 如果有多个数据项，启用批量打印
      if (printParams.items && Array.isArray(printParams.items) && printParams.items.length > 1) {
        printOptions.batchPrint = true;

        // 计算总页数
        const totalLabels = printParams.items.length;
        const totalPages = Math.ceil(totalLabels / printOptions.labelsPerPage);

        console.log(`总标签数: ${totalLabels}`);
        console.log(`总页数: ${totalPages}`);

        printOptions.totalPages = totalPages;
        printOptions.totalLabels = totalLabels;
      }

      console.log('=== 打印配置信息 ===');
      console.log('页面设置:', pageSettings);
      console.log('打印选项:', printOptions);
      console.log('打印数据数量:', printParams.items?.length || 0);
      console.log('模板配置:', templateConfig);

      // 调用客户端打印
      hiprintTemplate.print2(printParams.items, printOptions);

      EleMessage.success('打印任务已发送到客户端');
      showPrintPreview.value = false;
      showEnhancedPreview.value = false;

    } catch (error) {
      console.error('打印失败:', error);
      EleMessage.error('打印失败：' + error.message);
    }
  };

  // 处理预览刷新
  const handlePreviewRefresh = () => {
    console.log('预览已刷新');
    // 可以在这里添加刷新逻辑，比如重新获取数据
  };

  // 组件挂载时加载数据
  onMounted(() => {
    // loadCoinList();
  });
</script>

<style scoped>
  .batch-print-page {
    padding: 16px;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 16px;
  }

  :deep(.el-table) {
    font-size: 14px;
  }

  :deep(.el-table th) {
    background-color: #fafafa;
  }
</style>
