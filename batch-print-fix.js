/**
 * 批量打印修复补丁
 * 解决打印预览与实际打印不一致的问题
 * 
 * 使用方法：
 * 1. 在批量打印主文件中导入统一配置函数
 * 2. 替换原有的handleConfirmPrint方法
 * 3. 确保预览和打印使用相同的配置
 */

// 修复后的handleConfirmPrint方法
export const fixedHandleConfirmPrint = async (printParams) => {
  try {
    // 检查 hiprint 客户端连接状态
    if (!window.hiwebSocket || !window.hiwebSocket.opened) {
      // 客户端未连接，显示提示信息
      try {
        await ElMessageBox.confirm(
          '连接打印客户端失败！请确保目标服务器已下载并运行打印服务！是否尝试启动客户端？',
          '客户端未连接',
          {
            confirmButtonText: '启动客户端',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true,
            message: `
              <div>
                <p>连接打印客户端失败！</p>
              </div>
            `
          }
        );
        // 尝试通过 URL Scheme 启动客户端
        window.open('hiprint://', '_blank');
      } catch {
        // 用户取消
      }
      return;
    }

    // 客户端已连接，进行打印
    if (!printParams.layoutConfig || !printParams.items) {
      throw new Error('打印数据不完整');
    }

    // 动态导入 hiprint
    const { hiprint } = await import('vue-plugin-hiprint');
    
    // 导入统一配置函数
    const {
      createUnifiedPrintOptions,
      executeUnifiedPrint,
      getTemplatePaperInfo
    } = await import('@/utils/hiprint-config');

    // 创建 hiprint 模板实例
    const hiprintTemplate = new hiprint.PrintTemplate({
      template: printParams.layoutConfig
    });

    // 解析模板配置
    const templateConfig = printParams.layoutConfig;
    
    console.log('=== 使用统一打印配置 ===');
    console.log('模板配置:', templateConfig);
    console.log('打印数据数量:', printParams.items?.length || 0);

    // 使用统一的打印执行函数
    const result = await executeUnifiedPrint(hiprintTemplate, printParams, templateConfig);
    
    if (result.success) {
      EleMessage.success(result.message);
      showPrintPreview.value = false;
      showEnhancedPreview.value = false;
    } else {
      throw new Error(result.message);
    }

  } catch (error) {
    console.error('打印失败:', error);
    EleMessage.error('打印失败：' + error.message);
  }
};

// 修复后的预览渲染方法
export const fixedRenderPreview = async (printData) => {
  if (!printData?.items?.length || !printData?.layoutConfig) {
    throw new Error('没有可预览的数据或模板配置');
  }

  try {
    // 导入统一配置函数
    const {
      createUnifiedPreviewOptions,
      executeUnifiedPreview,
      getTemplatePaperInfo
    } = await import('@/utils/hiprint-config');
    
    // 导入hiprint配置工具
    const {
      initHiprint as initHiprintPlugin,
      createPrintTemplate
    } = await import('@/utils/hiprint-config');

    // 确保hiprint已初始化
    await initHiprintPlugin();

    // 使用原始模板配置创建一个hiprint模板实例
    const templateConfig = printData.layoutConfig;
    if (!templateConfig?.panels?.[0]) {
      throw new Error('模板配置无效');
    }

    // 创建hiprint模板实例
    const hiprintTemplate = createPrintTemplate(templateConfig, {
      dataMode: 1
    });

    console.log('=== 使用统一预览配置 ===');
    console.log('模板配置:', templateConfig);
    console.log('预览数据数量:', printData.items?.length || 0);

    // 使用统一的预览执行函数
    const result = await executeUnifiedPreview(hiprintTemplate, printData.items[0], templateConfig);
    
    if (result.success) {
      return result.htmlContent;
    } else {
      throw new Error(result.message);
    }

  } catch (error) {
    console.error('预览渲染失败:', error);
    throw error;
  }
};

// 导入语句修复
export const importStatements = `
// hiprint配置导入
import {
  createUnifiedPrintOptions,
  createUnifiedPreviewOptions,
  executeUnifiedPrint,
  executeUnifiedPreview,
  getTemplatePaperInfo
} from '@/utils/hiprint-config';
`;

// 使用说明
export const usageInstructions = `
使用修复方案的步骤：

1. 在批量打印主文件 (src/views/bank-note/batch-print/index.vue) 中：
   - 添加导入语句
   - 替换 handleConfirmPrint 方法

2. 在预览组件 (src/views/bank-note/batch-print/components/EnhancedPreview.vue) 中：
   - 添加导入语句
   - 替换 renderPreview 方法

3. 确保所有打印相关的配置都使用统一的配置函数

4. 测试验证：
   - 预览显示的布局应该与实际打印一致
   - 多标签模板应该正确显示为横向布局
   - 单标签模板应该正确显示为纵向布局
`;

export default {
  fixedHandleConfirmPrint,
  fixedRenderPreview,
  importStatements,
  usageInstructions
};
