<template>
  <div class="hiprint-font-demo">
    <el-card shadow="never">
      <template #header>
        <h3>hiprint 字体增强功能演示</h3>
      </template>

      <div class="demo-content">
        <!-- 左侧：字体预设面板 -->
        <div class="left-panel">
          <CoinLabelFontPresets 
            @apply-font="handleApplyFont"
            @copy-style="handleCopyStyle"
          />
        </div>

        <!-- 右侧：hiprint设计器 -->
        <div class="right-panel">
          <div class="designer-toolbar">
            <el-button @click="initDesigner" type="primary">初始化设计器</el-button>
            <el-button @click="addTextElement">添加文本元素</el-button>
            <el-button @click="previewTemplate">预览模板</el-button>
          </div>

          <!-- hiprint 设计器容器 -->
          <div class="hiprint-container">
            <div class="hiprint-panel-left">
              <!-- 元素库 -->
              <div class="element-library">
                <h4>元素库</h4>
                <div class="element-item" data-tid="defaultModule.text">
                  <i class="el-icon-edit"></i>
                  <span>文本</span>
                </div>
                <div class="element-item" data-tid="defaultModule.image">
                  <i class="el-icon-picture"></i>
                  <span>图片</span>
                </div>
              </div>
            </div>
            
            <div id="hiprint-panel-center" class="hiprint-panel-center">
              <!-- 设计区域 -->
            </div>
            
            <div class="hiprint-panel-right">
              <!-- hiprint官方属性面板 -->
              <div id="PrintElementOptionSetting" class="hiprint-option-setting">
                <!-- hiprint会自动在这里渲染属性面板 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 字体效果预览对话框 -->
    <el-dialog
      v-model="showPreview"
      title="字体效果预览"
      width="600px"
    >
      <div class="preview-content">
        <div class="preview-sample" :style="currentFontStyle">
          {{ previewText }}
        </div>
        <div class="style-info">
          <p><strong>字体：</strong>{{ currentFontStyle.fontFamily }}</p>
          <p><strong>大小：</strong>{{ currentFontStyle.fontSize }}</p>
          <p><strong>粗细：</strong>{{ currentFontStyle.fontWeight }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import CoinLabelFontPresets from '@/components/CoinLabelFontPresets.vue';
import { 
  initHiprint as initHiprintPlugin,
  createPrintTemplate,
  getHiprintInstance
} from '@/utils/hiprint-config';

// 响应式数据
const showPreview = ref(false);
const hiprintTemplate = ref(null);
const currentFontStyle = reactive({
  fontFamily: 'STKaiti, KaiTi, serif',
  fontSize: '16pt',
  fontWeight: 'normal'
});
const previewText = ref('中国人民银行 1980年贰角 68 Superb Gem Unc');

// 初始化设计器
const initDesigner = async () => {
  try {
    // 初始化 hiprint
    await initHiprintPlugin();
    
    // 创建模板实例
    hiprintTemplate.value = createPrintTemplate(null, {
      settingContainer: '#PrintElementOptionSetting'
    });

    // 设置纸张大小（钱币标签尺寸）
    hiprintTemplate.value.setPaper(80, 30); // 80mm x 30mm

    ElMessage.success('设计器初始化成功！字体选择器已增强');
  } catch (error) {
    console.error('初始化设计器失败:', error);
    ElMessage.error('初始化失败：' + error.message);
  }
};

// 添加文本元素
const addTextElement = () => {
  if (!hiprintTemplate.value) {
    ElMessage.warning('请先初始化设计器');
    return;
  }

  try {
    // 添加一个文本元素用于演示
    const textElement = {
      options: {
        left: 10,
        top: 10,
        height: 20,
        width: 150,
        text: '中国人民银行',
        fontSize: 14,
        fontFamily: 'STKaiti',
        color: '#333333'
      }
    };

    // 这里需要根据实际的hiprint API来添加元素
    // hiprintTemplate.value.addPrintElement(textElement);
    
    ElMessage.success('文本元素已添加，请在右侧属性面板中调整字体');
  } catch (error) {
    console.error('添加文本元素失败:', error);
    ElMessage.error('添加元素失败：' + error.message);
  }
};

// 预览模板
const previewTemplate = () => {
  if (!hiprintTemplate.value) {
    ElMessage.warning('请先初始化设计器');
    return;
  }

  try {
    // 获取模板JSON
    const templateJson = hiprintTemplate.value.getJson();
    console.log('模板JSON:', templateJson);
    
    // 这里可以实现预览功能
    ElMessage.success('模板预览功能开发中...');
  } catch (error) {
    console.error('预览失败:', error);
    ElMessage.error('预览失败：' + error.message);
  }
};

// 处理字体预设应用
const handleApplyFont = (fontConfig) => {
  console.log('应用字体配置:', fontConfig);
  
  // 更新当前字体样式用于预览
  Object.assign(currentFontStyle, fontConfig.style);
  
  // 如果有选中的元素，应用字体样式
  if (hiprintTemplate.value) {
    try {
      // 这里需要根据实际的hiprint API来更新选中元素的字体
      // hiprintTemplate.value.updateOption('fontFamily', fontConfig.style.fontFamily);
      // hiprintTemplate.value.updateOption('fontSize', fontConfig.style.fontSize);
      // hiprintTemplate.value.updateOption('fontWeight', fontConfig.style.fontWeight);
      
      ElMessage.success(`已应用${fontConfig.type}字体样式`);
    } catch (error) {
      console.error('应用字体失败:', error);
      ElMessage.error('应用字体失败：' + error.message);
    }
  }
  
  // 显示预览
  showPreview.value = true;
};

// 处理样式复制
const handleCopyStyle = (style) => {
  console.log('复制样式:', style);
  ElMessage.success('样式已复制到剪贴板');
};

// 组件挂载时初始化
onMounted(() => {
  console.log('hiprint字体增强演示页面已加载');
});
</script>

<style scoped>
.hiprint-font-demo {
  padding: 20px;
  height: calc(100vh - 120px);
}

.demo-content {
  display: flex;
  gap: 20px;
  height: 100%;
}

.left-panel {
  width: 350px;
  flex-shrink: 0;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.designer-toolbar {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  display: flex;
  gap: 12px;
}

.hiprint-container {
  flex: 1;
  display: flex;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.hiprint-panel-left {
  width: 200px;
  background: #f5f5f5;
  border-right: 1px solid #ddd;
}

.element-library {
  padding: 16px;
}

.element-library h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
}

.element-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.element-item:hover {
  background: #e6f7ff;
  border-color: #1890ff;
}

.hiprint-panel-center {
  flex: 1;
  background: #fff;
  position: relative;
  min-height: 400px;
}

.hiprint-panel-right {
  width: 300px;
  background: #f5f5f5;
  border-left: 1px solid #ddd;
}

.hiprint-option-setting {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

.preview-content {
  text-align: center;
}

.preview-sample {
  font-size: 24px;
  padding: 20px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  margin-bottom: 20px;
  background: #fafafa;
}

.style-info {
  text-align: left;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
}

.style-info p {
  margin: 8px 0;
  font-size: 14px;
}
</style>
