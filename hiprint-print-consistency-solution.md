# hiprint 打印预览与实际打印一致性解决方案

## 问题描述

**现象**：打印预览时显示的是一页多签横向布局，但调用hiprint客户端打印出来的是一页一签的纵向布局。

**根本原因**：
1. 预览配置与实际打印配置不一致
2. 缺少统一的纸张方向和布局配置
3. 批量打印参数设置不正确

## 解决方案

### 1. 新增统一配置函数

在 `src/utils/hiprint-config.js` 中新增了以下函数：

- `getTemplatePaperInfo(templateConfig)` - 获取模板纸张信息
- `createUnifiedPrintOptions(templateConfig, printParams)` - 创建统一的打印配置
- `createUnifiedPreviewOptions(templateConfig)` - 创建统一的预览配置
- `executeUnifiedPrint(hiprintTemplate, printParams, templateConfig)` - 执行统一打印
- `executeUnifiedPreview(hiprintTemplate, previewData, templateConfig)` - 执行统一预览

### 2. 关键配置参数

#### 纸张配置
```javascript
{
  width: templatePaperInfo.width, // mm
  height: templatePaperInfo.height, // mm
  orientation: templatePaperInfo.orientation, // portrait | landscape
  paperType: templatePaperInfo.paperType
}
```

#### 多标签布局配置
```javascript
{
  multiLabel: templatePaperInfo.labelsPerRow > 1,
  labelsPerRow: templatePaperInfo.labelsPerRow,
  labelsPerColumn: templatePaperInfo.labelsPerColumn,
  labelsPerPage: templatePaperInfo.labelsPerRow * templatePaperInfo.labelsPerColumn,
  labelSpacing: templatePaperInfo.labelSpacing
}
```

#### 打印质量配置
```javascript
{
  scale: 1, // 不缩放，保持原始尺寸
  fit: false, // 不自动适应页面
  shrinkToFit: false, // 不缩小适应
  useActualSize: true, // 使用实际尺寸
  dpi: 300, // 高质量打印DPI
  colorMode: 'color', // 支持彩色打印
  quality: 'high' // 高质量打印
}
```

### 3. 修改的文件

#### 3.1 `src/utils/hiprint-config.js`
- ✅ 新增统一配置函数
- ✅ 更新导出列表

#### 3.2 `src/views/bank-note/batch-print/components/EnhancedPreview.vue`
- ✅ 导入统一配置函数
- ✅ 修改预览渲染方法使用统一配置
- ✅ 简化打印处理方法

#### 3.3 `src/views/bank-note/batch-print/index.vue` (需要手动修改)
需要修改 `handleConfirmPrint` 方法：

```javascript
// 替换原有的复杂打印配置逻辑
import {
  createUnifiedPrintOptions,
  executeUnifiedPrint,
  getTemplatePaperInfo
} from '@/utils/hiprint-config';

// 在 handleConfirmPrint 方法中使用统一配置
const result = await executeUnifiedPrint(hiprintTemplate, printParams, templateConfig);
```

### 4. 使用方法

#### 4.1 在批量打印主文件中
```javascript
// 导入统一配置函数
import {
  createUnifiedPrintOptions,
  executeUnifiedPrint,
  getTemplatePaperInfo
} from '@/utils/hiprint-config';

// 在 handleConfirmPrint 方法中使用
const templateConfig = printParams.layoutConfig;
const result = await executeUnifiedPrint(hiprintTemplate, printParams, templateConfig);

if (result.success) {
  EleMessage.success(result.message);
} else {
  throw new Error(result.message);
}
```

#### 4.2 在预览组件中
```javascript
// 导入统一配置函数
import {
  createUnifiedPreviewOptions,
  executeUnifiedPreview
} from '@/utils/hiprint-config';

// 在预览渲染中使用
const result = await executeUnifiedPreview(hiprintTemplate, previewData, templateConfig);
```

### 5. 验证步骤

1. **预览测试**：
   - 打开批量打印页面
   - 选择多标签模板
   - 查看预览是否正确显示横向多标签布局

2. **打印测试**：
   - 确保hiprint客户端已连接
   - 执行打印操作
   - 验证实际打印结果与预览一致

3. **配置验证**：
   - 检查控制台输出的配置信息
   - 确认纸张方向、尺寸、标签数量等参数正确

### 6. 预期效果

- ✅ 预览显示的布局与实际打印完全一致
- ✅ 多标签模板正确显示为横向布局
- ✅ 单标签模板正确显示为纵向布局
- ✅ 纸张方向和尺寸配置统一
- ✅ 批量打印参数正确设置

### 7. 注意事项

1. **客户端连接**：确保hiprint客户端已正确安装并连接
2. **模板配置**：确保模板配置包含正确的纸张信息
3. **数据格式**：确保打印数据格式符合hiprint要求
4. **浏览器兼容性**：建议使用Chrome或Edge浏览器

### 8. 故障排除

如果仍然出现不一致问题：

1. 检查控制台是否有错误信息
2. 验证模板配置是否正确
3. 确认hiprint客户端版本兼容性
4. 检查纸张设置是否与模板匹配

## 总结

通过引入统一的配置管理机制，确保预览和打印使用相同的参数配置，从而解决了预览与实际打印不一致的问题。这个解决方案具有以下优势：

- **一致性**：预览和打印使用相同配置
- **可维护性**：统一的配置管理
- **扩展性**：易于添加新的配置选项
- **可靠性**：减少配置错误的可能性
