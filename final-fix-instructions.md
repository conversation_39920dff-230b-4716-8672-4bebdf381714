# hiprint 打印预览与实际打印一致性 - 最终修复指南

## 问题总结
**问题**：打印预览显示一页多签横向布局，但实际打印变成一页一签纵向布局

**解决方案**：统一预览和打印的配置参数，确保使用相同的纸张设置和布局配置

## 已完成的修改

### 1. ✅ 修改 `src/utils/hiprint-config.js`
- 新增 `getTemplatePaperInfo()` 函数
- 新增 `createUnifiedPrintOptions()` 函数  
- 新增 `createUnifiedPreviewOptions()` 函数
- 新增 `executeUnifiedPrint()` 函数
- 新增 `executeUnifiedPreview()` 函数
- 更新导出列表

### 2. ✅ 修改 `src/views/bank-note/batch-print/components/EnhancedPreview.vue`
- 导入统一配置函数
- 修改预览渲染方法使用统一配置
- 简化打印处理方法

## 需要手动完成的修改

### 3. ⚠️ 修改 `src/views/bank-note/batch-print/index.vue`

**步骤1：添加导入语句**
在文件顶部的导入部分添加：

```javascript
// hiprint配置导入
import {
  createUnifiedPrintOptions,
  executeUnifiedPrint,
  getTemplatePaperInfo
} from '@/utils/hiprint-config';
```

**步骤2：替换 handleConfirmPrint 方法**
找到 `handleConfirmPrint` 方法（大约在第325行），将整个方法替换为：

```javascript
// 处理确认打印 - 使用统一配置确保与预览一致 @since 2025-01-13
const handleConfirmPrint = async (printParams) => {
  try {
    // 检查 hiprint 客户端连接状态
    if (!window.hiwebSocket || !window.hiwebSocket.opened) {
      // 客户端未连接，显示提示信息
      try {
        await ElMessageBox.confirm(
          '连接打印客户端失败！请确保目标服务器已下载并运行打印服务！是否尝试启动客户端？',
          '客户端未连接',
          {
            confirmButtonText: '启动客户端',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true,
            message: `
              <div>
                <p>连接打印客户端失败！</p>
              </div>
            `
          }
        );
        // 尝试通过 URL Scheme 启动客户端
        window.open('hiprint://', '_blank');
      } catch {
        // 用户取消
      }
      return;
    }

    // 客户端已连接，进行打印
    if (!printParams.layoutConfig || !printParams.items) {
      throw new Error('打印数据不完整');
    }

    // 动态导入 hiprint
    const { hiprint } = await import('vue-plugin-hiprint');

    // 创建 hiprint 模板实例
    const hiprintTemplate = new hiprint.PrintTemplate({
      template: printParams.layoutConfig
    });

    // 解析模板配置
    const templateConfig = printParams.layoutConfig;
    
    console.log('=== 使用统一打印配置 ===');
    console.log('模板配置:', templateConfig);
    console.log('打印数据数量:', printParams.items?.length || 0);

    // 使用统一的打印执行函数
    const result = await executeUnifiedPrint(hiprintTemplate, printParams, templateConfig);
    
    if (result.success) {
      EleMessage.success(result.message);
      showPrintPreview.value = false;
      showEnhancedPreview.value = false;
    } else {
      throw new Error(result.message);
    }

  } catch (error) {
    console.error('打印失败:', error);
    EleMessage.error('打印失败：' + error.message);
  }
};
```

## 验证步骤

### 1. 功能测试
1. 重启开发服务器
2. 打开批量打印页面
3. 选择一个多标签模板
4. 点击预览，确认显示横向多标签布局
5. 确保hiprint客户端已连接
6. 执行打印，验证实际打印结果与预览一致

### 2. 控制台检查
打开浏览器开发者工具，查看控制台输出：
- 应该看到 "=== 统一打印配置 ===" 日志
- 应该看到 "=== 统一预览配置 ===" 日志
- 检查纸张信息、方向、标签数量等参数是否正确

### 3. 配置验证
确认以下配置参数正确：
- `orientation`: 应该是 'landscape'（横向）或 'portrait'（纵向）
- `labelsPerRow`: 每行标签数量
- `labelsPerPage`: 每页标签总数
- `width` 和 `height`: 纸张尺寸（mm）

## 预期结果

修复完成后应该达到以下效果：

✅ **预览一致性**：预览显示的布局与实际打印完全一致  
✅ **多标签支持**：多标签模板正确显示为横向布局  
✅ **单标签支持**：单标签模板正确显示为纵向布局  
✅ **配置统一**：预览和打印使用相同的纸张和布局配置  
✅ **质量保证**：打印质量和缩放比例正确  

## 故障排除

如果修复后仍有问题：

1. **检查导入**：确认所有导入语句正确
2. **检查语法**：确认没有语法错误
3. **重启服务**：重启开发服务器
4. **清除缓存**：清除浏览器缓存
5. **客户端版本**：确认hiprint客户端版本兼容

## 联系支持

如果按照以上步骤操作后仍有问题，请提供：
1. 控制台错误信息
2. 模板配置信息
3. hiprint客户端版本
4. 浏览器版本信息

---

**重要提醒**：请务必完成第3步的手动修改，这是解决问题的关键步骤！
