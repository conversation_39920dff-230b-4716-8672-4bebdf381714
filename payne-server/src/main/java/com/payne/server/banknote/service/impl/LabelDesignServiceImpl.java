package com.payne.server.banknote.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.server.banknote.entity.FieldDefinition;
import com.payne.server.banknote.entity.LabelTemplate;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.mapper.FieldDefinitionMapper;
import com.payne.server.banknote.mapper.LabelTemplateMapper;
import com.payne.server.banknote.service.LabelDesignService;
import com.payne.server.banknote.dto.LabelTemplateDto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签设计服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
@RequiredArgsConstructor
public class LabelDesignServiceImpl extends ServiceImpl<LabelTemplateMapper, LabelTemplate> 
        implements LabelDesignService {
    
    private final FieldDefinitionMapper fieldDefinitionMapper;
    
    @Override
    public List<FieldDefinition> getAvailableFields() {
        LambdaQueryWrapper<FieldDefinition> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FieldDefinition::getIsEnabled, true)
               .orderByAsc(FieldDefinition::getSortOrder);

        List<FieldDefinition> dbFields = fieldDefinitionMapper.selectList(wrapper);

        // 如果数据库中没有字段定义，则从PjOSendformItem实体中动态获取
        if (dbFields.isEmpty()) {
            return generateFieldDefinitionsFromEntity();
        }

        // 合并数据库字段和动态生成的字段，确保新字段总是可用
        List<FieldDefinition> dynamicFields = generateFieldDefinitionsFromEntity();
        List<FieldDefinition> mergedFields = new ArrayList<>(dbFields);

        // 添加数据库中不存在的动态字段
        for (FieldDefinition dynamicField : dynamicFields) {
            boolean exists = dbFields.stream()
                .anyMatch(dbField -> dbField.getFieldName().equals(dynamicField.getFieldName()));
            if (!exists) {
                mergedFields.add(dynamicField);
            }
        }

        // 按排序顺序重新排序
        mergedFields.sort((a, b) -> {
            Integer sortA = a.getSortOrder() != null ? a.getSortOrder() : Integer.MAX_VALUE;
            Integer sortB = b.getSortOrder() != null ? b.getSortOrder() : Integer.MAX_VALUE;
            return sortA.compareTo(sortB);
        });

        return mergedFields;
    }
    
    @Override
    public Map<String, List<FieldDefinition>> getFieldsByCategory() {
        List<FieldDefinition> fields = getAvailableFields();
        return fields.stream()
                .collect(Collectors.groupingBy(
                    field -> field.getCategory() != null ? field.getCategory() : "OTHER"
                ));
    }
    
    @Override
    public LabelTemplate saveTemplate(LabelTemplateDto templateDto) {
        LabelTemplate template = new LabelTemplate();
        BeanUtils.copyProperties(templateDto, template);
        
        if (template.getId() == null) {
            // 新增
            template.setCreateUser("system"); // TODO: 从当前用户获取
            template.setCreateTime(LocalDateTime.now());
            save(template);
        } else {
            // 更新
            template.setUpdateTime(LocalDateTime.now());
//            template.setUpdateUser("system"); // TODO: 从当前用户获取
            updateById(template);
        }
        
        return template;
    }
    
    @Override
    public List<LabelTemplate> getTemplateList() {
        LambdaQueryWrapper<LabelTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LabelTemplate::getStatus, "ACTIVE")
               .orderByDesc(LabelTemplate::getIsDefault)
               .orderByDesc(LabelTemplate::getCreateTime);
        
        return list(wrapper);
    }
    
    @Override
    public LabelTemplate getDefaultTemplate() {
        LambdaQueryWrapper<LabelTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LabelTemplate::getIsDefault, true)
               .eq(LabelTemplate::getStatus, "ACTIVE")
               .last("LIMIT 1");
        
        LabelTemplate defaultTemplate = getOne(wrapper);
        
        // 如果没有默认模板，创建一个基础模板
        if (defaultTemplate == null) {
            defaultTemplate = createBasicTemplate();
        }
        
        return defaultTemplate;
    }

    @Override
    public LabelTemplate setDefaultTemplate(String id) {
        // 先取消所有模板的默认状态
        LambdaUpdateWrapper<LabelTemplate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(LabelTemplate::getIsDefault, false);
        update(updateWrapper);

        // 设置指定模板为默认
        LabelTemplate template = getById(id);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        template.setIsDefault(true);
        template.setUpdateTime(LocalDateTime.now());
        updateById(template);

        return template;
    }

    @Override
    public LabelTemplate copyTemplate(String id, String newName) {
        // 获取原模板
        LabelTemplate originalTemplate = getById(id);
        if (originalTemplate == null) {
            throw new RuntimeException("原模板不存在");
        }

        // 检查新名称是否已存在
        LambdaQueryWrapper<LabelTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LabelTemplate::getTemplateName, newName)
               .eq(LabelTemplate::getStatus, "ACTIVE");

        if (count(wrapper) > 0) {
            throw new RuntimeException("模板名称已存在，请使用其他名称");
        }

        // 创建新模板
        LabelTemplate newTemplate = new LabelTemplate();
        newTemplate.setTemplateName(newName);
        newTemplate.setTemplateType(originalTemplate.getTemplateType());
        newTemplate.setLayoutConfig(originalTemplate.getLayoutConfig());
        newTemplate.setFieldMapping(originalTemplate.getFieldMapping());
        newTemplate.setColorConfig(originalTemplate.getColorConfig());
        newTemplate.setPageSettings(originalTemplate.getPageSettings());
        newTemplate.setDescription("复制自：" + originalTemplate.getTemplateName());
        newTemplate.setIsDefault(false); // 复制的模板不设为默认
        newTemplate.setStatus("ACTIVE");
        newTemplate.setCreateUser("system"); // TODO: 从当前用户获取
        newTemplate.setCreateTime(LocalDateTime.now());

        // 保存新模板
        save(newTemplate);

        return newTemplate;
    }

    /**
     * 从PjOSendformItem实体动态生成字段定义
     */
    private List<FieldDefinition> generateFieldDefinitionsFromEntity() {
        List<FieldDefinition> fields = new ArrayList<>();
        Field[] entityFields = PjOSendformItem.class.getDeclaredFields();

        int sortOrder = 1;
        for (Field field : entityFields) {
            // 跳过静态字段、特殊字段和技术字段
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                "serialVersionUID".equals(field.getName()) ||
                isExcludedField(field.getName())) {
                continue;
            }

            FieldDefinition def = new FieldDefinition();
            def.setFieldName(field.getName());
            def.setFieldType(field.getType().getSimpleName());
            def.setDisplayName(getFieldDisplayName(field.getName()));
            def.setCategory(getFieldCategory(field.getName()));
            def.setSortOrder(sortOrder++);
            def.setIsEnabled(true);
            def.setCreateTime(LocalDateTime.now());
            def.setCreateUser("system");

            fields.add(def);
        }

        // 添加自定义组合字段
        /*FieldDefinition serialWithVersionField = new FieldDefinition();
        serialWithVersionField.setFieldName("serialNumberWithVersion");
        serialWithVersionField.setFieldType("String");
        serialWithVersionField.setDisplayName("编号-版别");
        serialWithVersionField.setCategory("BASIC_INFO");
        serialWithVersionField.setSortOrder(sortOrder++);
        serialWithVersionField.setIsEnabled(true);
        serialWithVersionField.setCreateTime(LocalDateTime.now());
        serialWithVersionField.setCreateUser("system");
        fields.add(serialWithVersionField);*/

        // 添加二维码字段
        FieldDefinition qrCodeField = new FieldDefinition();
        qrCodeField.setFieldName("qrCode");
        qrCodeField.setFieldType("QRCode");
        qrCodeField.setDisplayName("二维码");
        qrCodeField.setCategory("SPECIAL");
        qrCodeField.setSortOrder(sortOrder++);
        qrCodeField.setIsEnabled(true);
        qrCodeField.setCreateTime(LocalDateTime.now());
        qrCodeField.setCreateUser("system");
        fields.add(qrCodeField);

        // 添加虚拟字段（不存在于实体类中的字段）
        // 评级分数（仅数字）字段
        FieldDefinition gradeScoreNumberField = new FieldDefinition();
        gradeScoreNumberField.setFieldName("gradeScoreNumber");
        gradeScoreNumberField.setFieldType("String");
        gradeScoreNumberField.setDisplayName("评级分数（仅数字）");
        gradeScoreNumberField.setCategory("GRADE_INFO");
        gradeScoreNumberField.setSortOrder(sortOrder++);
        gradeScoreNumberField.setIsEnabled(true);
        gradeScoreNumberField.setCreateTime(LocalDateTime.now());
        gradeScoreNumberField.setCreateUser("system");
        fields.add(gradeScoreNumberField);

        // 评级等级（仅文字）字段
        FieldDefinition gradeScoreTextField = new FieldDefinition();
        gradeScoreTextField.setFieldName("gradeScoreText");
        gradeScoreTextField.setFieldType("String");
        gradeScoreTextField.setDisplayName("评级等级（仅文字）");
        gradeScoreTextField.setCategory("GRADE_INFO");
        gradeScoreTextField.setSortOrder(sortOrder++);
        gradeScoreTextField.setIsEnabled(true);
        gradeScoreTextField.setCreateTime(LocalDateTime.now());
        gradeScoreTextField.setCreateUser("system");
        fields.add(gradeScoreTextField);

        return fields;
    }

    /**
     * 判断字段是否需要被排除
     */
    private boolean isExcludedField(String fieldName) {
        // 只保留指定的核心字段，其他都排除
        Set<String> allowedFields = Set.of(
            // 基础信息核心字段
            "diyCode", "bankName", "coinName1",
            "serialNumber", "version", "serialNumberWithVersion",
            // 评级信息字段
            "specialMark", "gradeScore", "gradeScoreNumber", "gradeScoreText",
            // 特殊字段
            "qrCode"
        );

        // 如果不在允许列表中，则排除
        return !allowedFields.contains(fieldName);
    }

    /**
     * 获取字段显示名称
     */
    private String getFieldDisplayName(String fieldName) {
        return switch (fieldName) {
            // 基础信息字段 - 只保留核心字段
            case "diyCode" -> "送评条码";
            case "bankName" -> "银行名称";
            case "coinName1" -> "钱币名称1";
            case "serialNumber" -> "钱币编号";
            case "version" -> "版别";

            // 评级信息字段
            case "specialMark" -> "特殊标记";
            case "gradeScore" -> "评级分数";
            case "gradeScoreNumber" -> "评级分数（仅数字）";
            case "gradeScoreText" -> "评级等级（仅文字）";

            // 特殊字段
            case "qrCode" -> "二维码";

            default -> fieldName;
        };
    }
    
    /**
     * 获取字段分类
     */
    private String getFieldCategory(String fieldName) {
        return switch (fieldName) {
            // 基础信息 - 只保留核心字段
            case "diyCode", "bankName", "coinName", "coinName1",
                 "serialNumber", "version", "serialNumberWithVersion" -> "BASIC_INFO";

            // 评级信息
            case "specialMark", "gradeScore", "gradeScoreNumber", "gradeScoreText" -> "GRADE_INFO";

            // 特殊字段（二维码等）
            case "qrCode" -> "SPECIAL";

            // 其他字段都归类到基础信息
            default -> "BASIC_INFO";
        };
    }
    
    /**
     * 创建基础模板
     */
    private LabelTemplate createBasicTemplate() {
        LabelTemplate template = new LabelTemplate();
        template.setTemplateName("默认标签模板");
        template.setTemplateType("STANDARD");
        template.setIsDefault(true);
        template.setStatus("ACTIVE");
        template.setCreateTime(LocalDateTime.now());
        template.setCreateUser("system");
        
        // 设置默认布局配置
        String defaultLayout = """
            {
                "zones": [
                    {"id": "logo", "name": "公司Logo", "x": 0, "y": 0, "width": 15, "height": 100},
                    {"id": "coinInfo", "name": "钱币信息", "x": 15, "y": 0, "width": 40, "height": 100},
                    {"id": "colorLabel", "name": "彩色标签", "x": 55, "y": 0, "width": 15, "height": 100},
                    {"id": "gradeInfo", "name": "评级信息", "x": 70, "y": 0, "width": 15, "height": 100},
                    {"id": "qrCode", "name": "二维码", "x": 85, "y": 0, "width": 15, "height": 100}
                ]
            }
            """;
        template.setLayoutConfig(defaultLayout);
        
        // 设置默认字段映射
        String defaultMapping = """
            {
                "coinInfo": ["bankName", "coinName1", "serialNumber", "version"],
                "gradeInfo": ["specialMark"]
            }
            """;
        template.setFieldMapping(defaultMapping);
        
        save(template);
        return template;
    }
}