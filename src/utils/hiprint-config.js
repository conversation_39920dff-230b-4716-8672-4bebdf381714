/**
 * hiprint 配置工具
 * 用于统一管理 hiprint 的初始化和配置
 */

import { applyCustomFontSizeConfig, createCustomFontSizeOption } from './hiprint-font-config';
import { applyEnhancedFontSelector } from './hiprint-font-selector';

let hiprintInstance = null;
let isInitialized = false;
let defaultProvider = null;

/**
 * 初始化 hiprint
 * @param {Object} options 配置选项
 * @returns {Promise} 返回 hiprint 实例
 */
export async function initHiprint(options = {}) {
  try {
    // 如果已经初始化，直接返回实例
    if (isInitialized && hiprintInstance) {
      return hiprintInstance;
    }

    // 动态导入 hiprint
    const { hiprint, defaultElementTypeProvider } = await import(
      'vue-plugin-hiprint'
    );

    // 默认配置
    const defaultConfig = {
      host: '', // 禁用WebSocket连接，避免连接错误
      token: '',
      debug: false,
      ...options
    };

    // 创建默认provider
    defaultProvider = new defaultElementTypeProvider();

    // 初始化 hiprint（先不注册自定义provider，使用默认的）
    hiprint.init({
      ...defaultConfig,
      providers: [defaultProvider]
    });

    // 应用自定义字体大小配置
    try {
      applyCustomFontSizeConfig(hiprint);
      console.log('自定义字体大小配置已应用');
    } catch (error) {
      console.warn('应用自定义字体大小配置失败:', error);
    }

    // 应用增强字体选择器
    try {
      applyEnhancedFontSelector(hiprint);
      console.log('增强字体选择器已应用');
    } catch (error) {
      console.warn('应用增强字体选择器失败:', error);
    }

    hiprintInstance = hiprint;
    isInitialized = true;

    console.log('hiprint 初始化成功');
    return hiprint;
  } catch (error) {
    console.error('hiprint 初始化失败:', error);
    throw new Error('hiprint 插件加载失败：' + error.message);
  }
}

/**
 * 获取元素模板配置
 * @param {string} tid 元素类型ID
 * @returns {Object|null} 元素模板
 */
export function getElementTemplate(tid) {
  return elementTemplates[tid] || null;
}

/**
 * 获取 hiprint 实例
 * @returns {Object|null} hiprint 实例
 */
export function getHiprintInstance() {
  return hiprintInstance;
}

/**
 * 获取默认provider
 * @returns {Object|null} provider实例
 */
export function getDefaultProvider() {
  return defaultProvider;
}

/**
 * 检查 hiprint 是否已初始化
 * @returns {boolean} 是否已初始化
 */
export function isHiprintInitialized() {
  return isInitialized;
}

/**
 * 创建打印模板
 * @param {Object} templateData 模板数据
 * @param {Object} options 配置选项
 * @returns {Object} 模板实例
 */
export function createPrintTemplate(templateData = null, options = {}) {
  if (!hiprintInstance) {
    throw new Error('hiprint 未初始化，请先调用 initHiprint()');
  }

  // 默认配置
  const defaultOptions = {
    settingContainer: '#PrintElementOptionSetting',
    paginationContainer: '.hiprint-printPagination',
    dataMode: 1, // 1:getJson 其他：getJsonTid 默认1
    history: true, // 是否需要 撤销重做功能
    // 预览模式配置 - 防止触发浏览器打印
    preview: options.settingContainer === null, // 如果没有设置容器，则为预览模式
    // 元素选中回调
    onElementSelect: (element) => {
      console.log('元素被选中:', element);
      // 触发全局回调
      if (
        designerConfig.onElementSelect &&
        typeof designerConfig.onElementSelect === 'function'
      ) {
        designerConfig.onElementSelect(element);
      }
    },
    // 元素变更回调
    onElementChange: (element, property, value) => {
      console.log('元素属性变更:', element, property, value);
      // 触发全局回调
      if (
        designerConfig.onElementChange &&
        typeof designerConfig.onElementChange === 'function'
      ) {
        designerConfig.onElementChange(element, property, value);
      }
    },
    // 模板数据变更回调
    onDataChanged: (type, json) => {
      console.log('模板数据变更:', type, json);
    },
    // 更新失败回调
    onUpdateError: (e) => {
      console.error('模板更新失败:', e);
    },
    // 自定义字体列表
    fontList: [
      // 常用中文字体
      { title: '微软雅黑', value: 'Microsoft YaHei' },
      { title: '宋体', value: 'SimSun' },
      { title: '黑体', value: 'STHeitiSC-Light' },

      // 楷体系列（适合正式文档）
      { title: '华文楷体', value: 'STKaiti' },
      { title: '楷体', value: 'KaiTi' },
      { title: '楷体GB2312', value: 'KaiTi_GB2312' },

      // 仿宋系列（适合官方文件）
      { title: '华文仿宋', value: 'STFangsong' },
      { title: '仿宋', value: 'FangSong' },
      { title: '仿宋GB2312', value: 'FangSong_GB2312' },

      // 其他中文字体
      { title: '华文中宋', value: 'STZhongsong' },
      { title: '华文新魏', value: 'STXinwei' },
      { title: '方正舒体', value: 'FZShuTi' },
      { title: '方正姚体', value: 'FZYaoti' },

      // 英文和数字字体
      { title: 'Arial', value: 'Arial' },
      { title: 'Arial Bold', value: 'Arial, sans-serif; font-weight: bold' },
      { title: 'Times New Roman', value: 'Times New Roman' },
      { title: 'Helvetica', value: 'Helvetica' },
      { title: 'Georgia', value: 'Georgia' },

      // 等宽字体（适合编号）
      { title: 'Courier New', value: 'Courier New' },
      { title: 'Monaco', value: 'Monaco' },
      { title: 'Consolas', value: 'Consolas' },

      // 特殊字体
      { title: 'cursive', value: 'cursive' }
    ]
  };

  // 合并配置
  const finalOptions = { ...defaultOptions, ...options };

  let template;
  if (templateData) {
    console.log('创建带数据的模板，数据结构:', {
      hasTemplate: !!templateData.template,
      hasPanels: !!templateData.panels,
      panelsLength: templateData.panels?.length || 0,
      templateKeys: Object.keys(templateData)
    });

    // 根据hiprint的构造函数要求，直接传入模板数据
    // 修复：确保传递正确的模板格式
    let templateConfig;
    if (templateData.panels) {
      // 如果已经是标准格式，直接使用
      templateConfig = templateData;
    } else if (templateData.template && templateData.template.panels) {
      // 如果包装在template属性中，提取出来
      templateConfig = templateData.template;
    } else {
      // 其他情况，尝试直接使用
      templateConfig = templateData;
    }

    console.log('最终传递给PrintTemplate的配置:', templateConfig);

    template = new hiprintInstance.PrintTemplate({
      template: templateConfig,
      ...finalOptions
    });

    console.log('模板创建后的状态:', {
      panelsCount: template.panels?.length || 0,
      hasDesignMethod: typeof template.design === 'function',
      hasGetJsonMethod: typeof template.getJson === 'function'
    });

    // 如果模板创建后没有面板，尝试手动添加
    if (!template.panels || template.panels.length === 0) {
      console.log('模板创建后没有面板，尝试从数据中恢复...');
      if (templateData.panels && Array.isArray(templateData.panels)) {
        // 尝试手动设置面板数据
        template.panels = templateData.panels;
        console.log('手动设置面板数据完成，面板数量:', template.panels.length);
      }
    }
  } else {
    console.log('创建空模板');
    // 创建空模板
    template = new hiprintInstance.PrintTemplate({
      template: {},
      ...finalOptions
    });
  }

  console.log('创建打印模板成功:', template);
  return template;
}

/**
 * 创建预览专用的打印模板
 * @param {Object} templateData 模板数据
 * @param {Object} options 配置选项
 * @returns {Object} 模板实例
 */
export function createPreviewTemplate(templateData = null, options = {}) {
  // 预览专用配置
  const previewOptions = {
    settingContainer: null, // 预览时不需要属性面板
    paginationContainer: null, // 预览时不需要分页
    history: false, // 预览时不需要历史记录
    preview: true, // 明确标记为预览模式
    // 禁用可能触发打印的回调
    onDataChanged: null,
    onUpdateError: null,
    ...options
  };

  return createPrintTemplate(templateData, previewOptions);
}

/**
 * 获取模板的纸张信息
 * @param {Object} template hiprint模板实例或模板数据
 * @returns {Object} 纸张信息
 */
export function getTemplatePaperInfo(template) {
  try {
    let width = 210; // 默认A4宽度
    let height = 297; // 默认A4高度
    let paperType = 'A4';
    let orientation = 'portrait';

    // 如果是模板实例
    if (template && typeof template.getJson === 'function') {
      const templateData = template.getJson();
      if (
        templateData &&
        templateData.panels &&
        templateData.panels.length > 0
      ) {
        const panel = templateData.panels[0];
        if (panel.width && panel.height) {
          width = panel.width;
          height = panel.height;
        }
      }
    }
    // 如果是模板数据
    else if (template && template.panels && template.panels.length > 0) {
      const panel = template.panels[0];
      if (panel.width && panel.height) {
        width = panel.width;
        height = panel.height;
      }
    }
    // 如果是单个面板数据
    else if (template && template.width && template.height) {
      width = template.width;
      height = template.height;
    }

    // 判断纸张类型和方向
    const paperInfo = detectPaperTypeAndOrientation(width, height);

    return {
      width,
      height,
      paperType: paperInfo.paperType,
      orientation: paperInfo.orientation,
      isCustom: paperInfo.isCustom
    };
  } catch (error) {
    console.error('获取模板纸张信息失败:', error);
    return {
      width: 210,
      height: 297,
      paperType: 'A4',
      orientation: 'portrait',
      isCustom: false
    };
  }
}

/**
 * 检测纸张类型和方向
 * @param {number} width 宽度
 * @param {number} height 高度
 * @returns {Object} 纸张类型和方向信息
 */
export function detectPaperTypeAndOrientation(width, height) {
  // 标准纸张尺寸检测
  const standardPapers = [
    { type: 'A3', width: 297, height: 420 },
    { type: 'A4', width: 210, height: 297 },
    { type: 'A5', width: 148, height: 210 },
    { type: 'B3', width: 353, height: 500 },
    { type: 'B4', width: 250, height: 353 },
    { type: 'B5', width: 176, height: 250 }
  ];

  // 检查是否匹配标准纸张（纵向）
  for (const paper of standardPapers) {
    if (
      Math.abs(width - paper.width) <= 1 &&
      Math.abs(height - paper.height) <= 1
    ) {
      return {
        paperType: paper.type,
        orientation: 'portrait',
        isCustom: false
      };
    }
    // 检查是否匹配标准纸张（横向）
    if (
      Math.abs(width - paper.height) <= 1 &&
      Math.abs(height - paper.width) <= 1
    ) {
      return {
        paperType: paper.type,
        orientation: 'landscape',
        isCustom: false
      };
    }
  }

  // 如果不匹配任何标准纸张，则为自定义纸张
  // 判断方向：通常宽度小于高度为纵向
  const orientation = width <= height ? 'portrait' : 'landscape';

  return {
    paperType: 'custom',
    orientation,
    isCustom: true
  };
}

/**
 * 设计器配置 - 恢复hiprint官方默认配置
 */
export const designerConfig = {
  // hiprint官方属性面板配置
  settingContainer: '#PrintElementOptionSetting',
  // 元素选中回调
  onElementSelect: null,
  // 元素属性变更回调
  onElementChange: null,
  // 元素添加回调
  onElementAdd: null,
  // 元素删除回调
  onElementDelete: null
};

/**
 * 预览配置
 */
export const previewConfig = {
  preview: true,
  width: '100%',
  height: 'auto'
};

/**
 * 打印配置
 */
export const printConfig = {
  margin: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10
  },
  pageSize: 'A4',
  orientation: 'portrait' // portrait | landscape
};

/**
 * 获取模板纸张信息的增强函数
 * @since 2025-01-13
 */
export function getTemplatePaperInfo(templateConfig) {
  if (!templateConfig || !templateConfig.panels || !templateConfig.panels[0]) {
    return {
      width: 210,
      height: 297,
      orientation: 'portrait',
      paperType: 'A4',
      labelsPerRow: 1,
      labelsPerColumn: 1,
      labelSpacing: { horizontal: 0, vertical: 0 },
      margin: { top: 10, right: 10, bottom: 10, left: 10 }
    };
  }

  const panel = templateConfig.panels[0];
  const paperInfo = {
    width: panel.width || 210,
    height: panel.height || 297,
    orientation: panel.width > panel.height ? 'landscape' : 'portrait',
    paperType: 'CUSTOM',
    margin: {
      top: panel.paperNumberTop || 10,
      right: panel.paperNumberLeft || 10,
      bottom: panel.paperNumberTop || 10,
      left: panel.paperNumberLeft || 10
    }
  };

  // 分析模板布局，确定是否为多标签模板
  const elements = panel.printElements || [];
  if (elements.length > 0) {
    // 检查是否有重复的元素位置，判断是否为多标签布局
    const positions = elements.map(el => ({ x: el.options.left, y: el.options.top }));
    const uniquePositions = [...new Set(positions.map(p => `${p.x},${p.y}`))];

    if (positions.length > uniquePositions.length) {
      // 有重复位置，可能是多标签模板
      paperInfo.labelsPerRow = 2; // 默认每行2个标签
      paperInfo.labelsPerColumn = 1;
      paperInfo.labelSpacing = { horizontal: 10, vertical: 5 };
    } else {
      paperInfo.labelsPerRow = 1;
      paperInfo.labelsPerColumn = 1;
      paperInfo.labelSpacing = { horizontal: 0, vertical: 0 };
    }
  }

  return paperInfo;
}

/**
 * 创建统一的打印配置，确保预览与实际打印一致
 * @since 2025-01-13
 */
export function createUnifiedPrintOptions(templateConfig, printParams) {
  const templatePaperInfo = getTemplatePaperInfo(templateConfig);

  console.log('=== 统一打印配置 ===');
  console.log('模板纸张信息:', templatePaperInfo);

  const printOptions = {
    // 基础打印设置
    preview: false, // 不显示预览对话框，直接打印
    printer: '', // 使用默认打印机
    title: '批量标签打印',

    // 纸张设置 - 使用模板的实际纸张配置
    paperType: templatePaperInfo.paperType,
    width: templatePaperInfo.width, // mm
    height: templatePaperInfo.height, // mm

    // 页面方向设置 - 关键：确保与预览一致
    orientation: templatePaperInfo.orientation,

    // 页面边距设置
    margin: templatePaperInfo.margin,

    // 批量打印配置
    batchPrint: true, // 启用批量打印
    autoPage: true, // 启用自动分页
    pageBreak: false, // 禁用强制分页，让多标签在同一页显示

    // 多标签布局配置
    multiLabel: templatePaperInfo.labelsPerRow > 1 || templatePaperInfo.labelsPerColumn > 1,
    labelsPerRow: templatePaperInfo.labelsPerRow,
    labelsPerColumn: templatePaperInfo.labelsPerColumn,
    labelsPerPage: templatePaperInfo.labelsPerRow * templatePaperInfo.labelsPerColumn,

    // 标签间距设置
    labelSpacing: templatePaperInfo.labelSpacing,

    // 打印质量设置
    scale: 1, // 不缩放，保持原始尺寸
    fit: false, // 不自动适应页面
    shrinkToFit: false, // 不缩小适应
    useActualSize: true, // 使用实际尺寸
    dpi: 300, // 高质量打印DPI

    // 颜色模式设置
    colorMode: 'color', // 支持彩色打印
    quality: 'high', // 高质量打印

    // 打印模式
    mode: templatePaperInfo.labelsPerRow > 1 ? 'batch-labels' : 'single-label',

    // 打印份数和双面设置
    copies: 1,
    duplex: 'none', // 单面打印

    // 纸张来源
    paperSource: 'auto'
  };

  // 如果有多个数据项，计算分页信息
  if (printParams.items && Array.isArray(printParams.items) && printParams.items.length > 1) {
    const totalLabels = printParams.items.length;
    const labelsPerPage = printOptions.labelsPerPage;
    const totalPages = Math.ceil(totalLabels / labelsPerPage);

    console.log(`=== 批量打印信息 ===`);
    console.log(`总标签数: ${totalLabels}`);
    console.log(`每页标签数: ${labelsPerPage}`);
    console.log(`总页数: ${totalPages}`);
    console.log(`纸张方向: ${printOptions.orientation}`);
    console.log(`纸张尺寸: ${printOptions.width}×${printOptions.height}mm`);

    printOptions.totalPages = totalPages;
    printOptions.totalLabels = totalLabels;
  }

  return printOptions;
}

/**
 * 修复预览配置，确保与打印一致
 * @since 2025-01-13
 */
export function createUnifiedPreviewOptions(templateConfig) {
  const templatePaperInfo = getTemplatePaperInfo(templateConfig);

  return {
    preview: true,
    width: templatePaperInfo.width + 'mm',
    height: templatePaperInfo.height + 'mm',
    orientation: templatePaperInfo.orientation,
    margin: templatePaperInfo.margin,
    scale: 1,
    // 预览时也要考虑多标签布局
    multiLabel: templatePaperInfo.labelsPerRow > 1,
    labelsPerRow: templatePaperInfo.labelsPerRow,
    labelsPerColumn: templatePaperInfo.labelsPerColumn
  };
}

/**
 * 打印执行函数，确保预览与实际打印一致
 * @since 2025-01-13
 */
export async function executeUnifiedPrint(hiprintTemplate, printParams, templateConfig) {
  try {
    // 创建统一的打印配置
    const printOptions = createUnifiedPrintOptions(templateConfig, printParams);

    console.log('=== 执行统一打印 ===');
    console.log('打印配置:', printOptions);
    console.log('打印数据数量:', printParams.items?.length || 0);

    // 使用hiprint的print2方法进行打印
    hiprintTemplate.print2(printParams.items, printOptions);

    return {
      success: true,
      message: '打印任务已发送到客户端',
      config: printOptions
    };
  } catch (error) {
    console.error('统一打印执行失败:', error);
    return {
      success: false,
      message: '打印失败：' + error.message,
      error
    };
  }
}

/**
 * 预览执行函数，确保与打印配置一致
 * @since 2025-01-13
 */
export async function executeUnifiedPreview(hiprintTemplate, previewData, templateConfig) {
  try {
    // 创建统一的预览配置
    const previewOptions = createUnifiedPreviewOptions(templateConfig);

    console.log('=== 执行统一预览 ===');
    console.log('预览配置:', previewOptions);

    // 使用相同的配置进行预览渲染
    const htmlContent = hiprintTemplate.getHtml(previewData, previewOptions);

    return {
      success: true,
      htmlContent,
      config: previewOptions
    };
  } catch (error) {
    console.error('统一预览执行失败:', error);
    return {
      success: false,
      message: '预览失败：' + error.message,
      error
    };
  }
}

/**
 * 纸张规格定义 (单位: mm)
 */
export const paperSizes = {
  A3: { width: 297, height: 420, name: 'A3' },
  A4: { width: 210, height: 297, name: 'A4' },
  A5: { width: 148, height: 210, name: 'A5' },
  B3: { width: 353, height: 500, name: 'B3' },
  B4: { width: 250, height: 353, name: 'B4' },
  B5: { width: 176, height: 250, name: 'B5' }
};

/**
 * 设置模板纸张大小
 * @param {Object} template hiprint模板实例
 * @param {string|Object} paperType 纸张类型或自定义尺寸对象
 * @param {number} customWidth 自定义宽度(mm)
 * @param {number} customHeight 自定义高度(mm)
 */
export function setPaperSize(
  template,
  paperType,
  customWidth = null,
  customHeight = null
) {
  if (!template) {
    throw new Error('模板实例不存在');
  }

  let width, height;

  if (typeof paperType === 'string' && paperSizes[paperType]) {
    // 使用预定义纸张规格
    const paper = paperSizes[paperType];
    width = paper.width;
    height = paper.height;
  } else if (
    typeof paperType === 'object' &&
    paperType.width &&
    paperType.height
  ) {
    // 使用传入的纸张对象
    width = paperType.width;
    height = paperType.height;
  } else if (customWidth && customHeight) {
    // 使用自定义尺寸
    width = customWidth;
    height = customHeight;
  } else {
    throw new Error('无效的纸张规格参数');
  }

  // 应用纸张设置
  if (typeof template.setPaper === 'function') {
    try {
      template.setPaper(width, height);
      console.log(`设置纸张大小: ${width}×${height}mm`);
      return { width, height, success: true };
    } catch (error) {
      console.error('设置纸张大小时发生错误:', error);
      return { width, height, success: false, error: error.message };
    }
  } else {
    console.warn('模板不支持setPaper方法');
    return { width, height, success: false, error: '模板不支持setPaper方法' };
  }
}

/**
 * 安全地设置纸张大小（带重试机制）
 * @param {Object} template hiprint模板实例
 * @param {number} width 宽度(mm)
 * @param {number} height 高度(mm)
 * @param {number} maxRetries 最大重试次数
 * @returns {Promise} 设置结果
 */
export function safeSetPaperSize(template, width, height, maxRetries = 3) {
  return new Promise((resolve) => {
    let retryCount = 0;

    const attemptSetPaper = () => {
      try {
        // 基础验证
        if (!template) {
          resolve({ success: false, error: '模板实例不存在' });
          return;
        }

        if (typeof template.setPaper !== 'function') {
          resolve({ success: false, error: '模板不支持setPaper方法' });
          return;
        }

        // 检查模板是否已经初始化完成
        if (!template.editingPanel && !template.panels) {
          throw new Error('模板尚未完全初始化，请稍后重试');
        }

        // 尝试设置纸张大小
        template.setPaper(width, height);
        console.log(`设置纸张大小成功: ${width}×${height}mm`);
        resolve({ success: true, width, height });
      } catch (error) {
        retryCount++;
        console.warn(
          `设置纸张大小失败 (尝试 ${retryCount}/${maxRetries}):`,
          error.message
        );

        // 特殊错误处理
        if (
          error.message.includes('resize') ||
          error.message.includes('undefined')
        ) {
          console.log('检测到resize相关错误，可能是模板未完全初始化');
        }

        if (retryCount < maxRetries) {
          // 延迟重试，每次延迟时间递增
          setTimeout(attemptSetPaper, 300 * retryCount);
        } else {
          resolve({
            success: false,
            error: error.message,
            retryCount,
            originalError: error
          });
        }
      }
    };

    // 首次尝试前稍微延迟，确保模板初始化
    setTimeout(attemptSetPaper, 100);
  });
}

/**
 * 常用元素模板 - 基于 hiprint 标准格式
 */
export const elementTemplates = {
  text: {
    tid: 'text',
    title: '文本',
    type: 'text',
    options: {
      left: 50,
      top: 50,
      height: 30,
      width: 200,
      title: '文本',
      text: '文本内容',
      fontSize: 14,
      fontFamily: 'Microsoft YaHei',
      color: '#000000',
      textAlign: 'left',
      lineHeight: 1.2,
      backgroundColor: '#ffffff',
      borderWidth: 1,
      borderStyle: 'solid',
      borderColor: '#cccccc'
    }
  },
  image: {
    tid: 'image',
    title: '图片',
    type: 'image',
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 100,
      title: '图片',
      src: '',
      fit: 'contain'
    }
  },
  barcode: {
    tid: 'barcode',
    title: '条形码',
    type: 'barcode',
    options: {
      left: 50,
      top: 50,
      height: 50,
      width: 200,
      title: '条形码',
      text: '123456789',
      code: 'CODE128',
      fontSize: 12
    }
  },
  qrcode: {
    tid: 'qrcode',
    title: '二维码',
    type: 'qrcode',
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 100,
      title: '二维码',
      text: 'https://example.com'
    }
  },
  line: {
    tid: 'line',
    title: '线条',
    type: 'hline',
    options: {
      left: 50,
      top: 50,
      height: 1,
      width: 200,
      title: '线条'
    }
  },
  rect: {
    tid: 'rect',
    title: '矩形',
    type: 'rect',
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 200,
      title: '矩形'
    }
  }
};

/**
 * 字段类型映射
 */
export const fieldTypeMap = {
  String: 'text',
  Integer: 'text',
  Long: 'text',
  Double: 'text',
  BigDecimal: 'text',
  Date: 'text',
  LocalDateTime: 'text',
  Boolean: 'text'
};

/**
 * 获取字段对应的元素模板
 * @param {string} fieldType 字段类型
 * @param {string} fieldName 字段名称
 * @param {string} displayName 显示名称
 * @returns {Object} 元素模板
 */
export function getFieldElementTemplate(fieldType, fieldName, displayName) {
  const elementType = fieldTypeMap[fieldType] || 'text';
  const template = { ...elementTemplates[elementType] };

  // 设置字段相关属性
  template.options.title = displayName || fieldName;
  template.options.field = fieldName;
  template.options.fieldType = fieldType;

  return template;
}

/**
 * 错误处理
 */
export function handleHiprintError(error) {
  console.error('hiprint 错误:', error);

  // 根据错误类型提供不同的处理建议
  if (error.message.includes('WebSocket')) {
    console.warn('WebSocket 连接失败，这是正常的，不影响设计器功能');
    return '打印服务连接失败，但设计器功能正常';
  } else if (error.message.includes('PrintTemplate')) {
    return '模板创建失败，请检查模板数据格式';
  } else if (error.message.includes('design')) {
    return '设计器初始化失败，请刷新页面重试';
  } else {
    return '未知错误：' + error.message;
  }
}

/**
 * 检测 hiprint API
 * @returns {Object} API 信息
 */
export function detectHiprintAPI() {
  if (!hiprintInstance) {
    return { error: 'hiprint 未初始化' };
  }

  const template = new hiprintInstance.PrintTemplate();
  const apiInfo = {
    templateMethods: Object.getOwnPropertyNames(template),
    templatePrototype: Object.getOwnPropertyNames(
      Object.getPrototypeOf(template)
    ),
    hasAddPrintElement: typeof template.addPrintElement === 'function',
    hasAddElement: typeof template.addElement === 'function',
    hasAddPrintPanel: typeof template.addPrintPanel === 'function',
    hasDesign: typeof template.design === 'function',
    hasGetJson: typeof template.getJson === 'function',
    panels: template.panels || null
  };

  console.log('hiprint API 检测结果:', apiInfo);
  return apiInfo;
}

/**
 * 构建可拖拽元素
 * @param {string} containerId 容器ID
 * @returns {Promise} 返回构建结果
 */
export function buildDraggableElements(containerId = '.ep-draggable-item') {
  return new Promise((resolve, reject) => {
    if (!hiprintInstance) {
      const error = 'hiprint未初始化，无法构建拖拽元素';
      console.warn(error);
      reject(new Error(error));
      return;
    }

    try {
      // 等待DOM元素完全渲染
      setTimeout(() => {
        const elements = document.querySelectorAll(containerId);
        console.log(`找到 ${elements.length} 个拖拽元素`);

        if (elements.length === 0) {
          const error = '未找到拖拽元素，请检查ElementLibrary组件是否正确渲染';
          console.warn(error);
          reject(new Error(error));
          return;
        }

        // 检查jQuery是否可用
        if (!window.$) {
          const error = 'jQuery未加载，无法构建拖拽元素';
          console.error(error);
          reject(new Error(error));
          return;
        }

        // 使用jQuery选择器构建拖拽元素
        const $elements = window.$(containerId);
        if ($elements.length === 0) {
          const error = 'jQuery未找到拖拽元素';
          console.warn(error);
          reject(new Error(error));
          return;
        }

        // 验证每个元素是否有正确的tid属性
        let validElements = 0;
        $elements.each(function () {
          const tid = $(this).attr('tid');
          if (tid) {
            validElements++;
            console.log(
              `有效拖拽元素: tid=${tid}, text=${$(this).text().trim()}`
            );
          } else {
            console.warn('拖拽元素缺少tid属性:', this);
          }
        });

        if (validElements === 0) {
          const error = '没有找到有效的拖拽元素（缺少tid属性）';
          console.error(error);
          reject(new Error(error));
          return;
        }

        // 构建拖拽元素
        try {
          hiprintInstance.PrintElementTypeManager.buildByHtml($elements);
          console.log(`拖拽元素构建成功，共 ${validElements} 个有效元素`);
          resolve({ success: true, count: validElements });
        } catch (buildError) {
          console.error('构建拖拽元素时发生错误:', buildError);
          reject(buildError);
        }
      }, 200); // 增加延迟确保DOM完全渲染
    } catch (error) {
      console.error('构建拖拽元素失败:', error);
      reject(error);
    }
  });
}

/**
 * 安全添加元素到模板
 * @param {Object} template 模板实例
 * @param {Object} elementConfig 元素配置
 * @returns {Object} 添加结果
 */
export function safeAddElement(template, elementConfig) {
  console.log('开始添加元素:', elementConfig);

  try {
    // 确保有面板存在
    let panel = null;

    // 检查是否已有面板
    if (
      template.panels &&
      Array.isArray(template.panels) &&
      template.panels.length > 0
    ) {
      panel = template.panels[0];
      console.log('使用现有面板:', panel);
    } else {
      // 创建新面板 - 这是 hiprint 的标准做法
      if (typeof template.addPrintPanel === 'function') {
        panel = template.addPrintPanel();
        console.log('创建新面板:', panel);
      }
    }

    if (!panel) {
      return { success: false, error: '无法创建或获取面板' };
    }

    // 确保元素配置有正确的类型和唯一ID
    if (!elementConfig.tid) {
      elementConfig.tid =
        'elem_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
    }

    // 完善元素基本属性
    if (!elementConfig.options) {
      elementConfig.options = {};
    }

    // 设置一些默认属性，确保元素可见
    if (!elementConfig.options.backgroundColor) {
      elementConfig.options.backgroundColor = '#ffffff';
    }

    if (!elementConfig.options.borderWidth) {
      elementConfig.options.borderWidth = 1;
    }

    if (!elementConfig.options.borderColor) {
      elementConfig.options.borderColor = '#000000';
    }

    if (!elementConfig.options.borderStyle) {
      elementConfig.options.borderStyle = 'solid';
    }

    // 确保位置属性
    if (typeof elementConfig.options.left !== 'number') {
      elementConfig.options.left = 50;
    }

    if (typeof elementConfig.options.top !== 'number') {
      elementConfig.options.top = 50;
    }

    // 根据元素类型使用不同的添加方法
    const elementType = elementConfig.type || elementConfig.tid || 'text';
    console.log('元素类型:', elementType);

    let result = null;
    let addMethod = null;

    // 直接使用面板原始API添加元素（绕过封装的方法）
    if (
      typeof panel.printElements === 'object' &&
      Array.isArray(panel.printElements) &&
      panel.createPrintElement &&
      typeof panel.createPrintElement === 'function'
    ) {
      try {
        // 使用原始API直接创建并添加元素
        console.log('尝试使用原始API添加元素');
        const printElement = panel.createPrintElement(elementConfig);
        if (printElement) {
          panel.printElements.push(printElement);
          result = printElement;
          addMethod = 'panel.printElements.push';
          console.log('使用原始API添加元素成功:', result);
        }
      } catch (error) {
        console.error('使用原始API添加元素失败:', error);
      }
    }

    // 如果直接添加失败，尝试使用特定类型的添加方法
    if (!result) {
      switch (elementType) {
        case 'text':
          if (typeof panel.addPrintText === 'function') {
            try {
              // 确保文本元素有必要的属性
              if (!elementConfig.options.text)
                elementConfig.options.text = '文本内容';

              result = panel.addPrintText(elementConfig);
              addMethod = 'panel.addPrintText';
              console.log('使用 addPrintText 成功:', result);
            } catch (error) {
              console.error('addPrintText 执行失败:', error);
            }
          }
          break;

        case 'image':
          if (typeof panel.addPrintImage === 'function') {
            try {
              result = panel.addPrintImage(elementConfig);
              addMethod = 'panel.addPrintImage';
              console.log('使用 addPrintImage 成功:', result);
            } catch (error) {
              console.error('addPrintImage 执行失败:', error);
            }
          }
          break;

        case 'hline':
        case 'line':
          if (typeof panel.addPrintHline === 'function') {
            try {
              result = panel.addPrintHline(elementConfig);
              addMethod = 'panel.addPrintHline';
              console.log('使用 addPrintHline 成功:', result);
            } catch (error) {
              console.error('addPrintHline 执行失败:', error);
            }
          }
          break;

        case 'rect':
          if (typeof panel.addPrintRect === 'function') {
            try {
              result = panel.addPrintRect(elementConfig);
              addMethod = 'panel.addPrintRect';
              console.log('使用 addPrintRect 成功:', result);
            } catch (error) {
              console.error('addPrintRect 执行失败:', error);
            }
          }
          break;

        case 'field':
          // 处理数据字段元素
          if (typeof panel.addPrintText === 'function') {
            try {
              // 确保有字段名称
              if (!elementConfig.options.field && elementConfig.key) {
                elementConfig.options.field = elementConfig.key;
                elementConfig.options.title = elementConfig.key;
              }

              result = panel.addPrintText(elementConfig);
              addMethod = 'panel.addPrintText(field)';
              console.log('使用 addPrintText 添加字段成功:', result);
            } catch (error) {
              console.error('添加字段执行失败:', error);
            }
          }
          break;
      }
    }

    // 如果特定方法不存在或失败，尝试通用方法
    if (!result && typeof panel.addPrintElement === 'function') {
      try {
        result = panel.addPrintElement(elementConfig);
        addMethod = 'panel.addPrintElement';
        console.log('使用 addPrintElement 成功:', result);
      } catch (error) {
        console.error('addPrintElement 执行失败:', error);
      }
    }

    if (!result && typeof panel.addElement === 'function') {
      try {
        result = panel.addElement(elementConfig);
        addMethod = 'panel.addElement';
        console.log('使用 addElement 成功:', result);
      } catch (error) {
        console.error('addElement 执行失败:', error);
      }
    }

    // 最后尝试直接在模板上添加
    if (!result && typeof template.addPrintElement === 'function') {
      try {
        result = template.addPrintElement(elementConfig);
        addMethod = 'template.addPrintElement';
        console.log('使用 template.addPrintElement 成功:', result);
      } catch (error) {
        console.error('template.addPrintElement 执行失败:', error);
      }
    }

    // 如果成功添加了元素
    if (result !== null && addMethod) {
      // 检查并更新DOM
      try {
        // 如果有DOM操作方法，直接添加元素到DOM
        if (result && typeof result.designTarget === 'function') {
          const $target = result.designTarget();
          if ($target) {
            console.log('元素已有设计目标');
          } else if (
            result.createDesignTarget &&
            typeof result.createDesignTarget === 'function'
          ) {
            console.log('尝试创建设计目标');
            result.createDesignTarget();
          }
        }

        // 尝试强制刷新面板显示
        if (panel.hideDesignOptions) {
          panel.hideDesignOptions();
        }

        if (
          panel.updatePrintElements &&
          typeof panel.updatePrintElements === 'function'
        ) {
          console.log('尝试更新面板元素');
          panel.updatePrintElements();
        }

        // 尝试刷新设计器显示
        if (typeof template.setPaper === 'function') {
          // 重新设置纸张大小可以强制重绘
          const paperType = template.getPaperType
            ? template.getPaperType()
            : 'A4';
          template.setPaper(paperType);
          console.log('重新设置纸张类型触发刷新:', paperType);
        }

        // 手动调用重绘方法
        if (typeof template.refresh === 'function') {
          template.refresh();
          console.log('手动调用模板刷新方法');
        }

        // 检查面板元素
        setTimeout(() => {
          try {
            const elements = panel.printElements || panel.elements || [];
            const elementCount = Array.isArray(elements) ? elements.length : 0;
            console.log('面板元素数量:', elementCount);
            console.log('面板对象:', panel);
            console.log(
              '模板面板数量:',
              template.panels ? template.panels.length : 0
            );

            if (elementCount === 0) {
              console.warn('警告: 面板元素数组为空，可能需要重新加载设计器');
            } else {
              console.log('元素列表:', elements);
            }
          } catch (e) {
            console.error('检查面板元素时出错:', e);
          }
        }, 300);
      } catch (verifyError) {
        console.warn('验证元素添加时出错:', verifyError);
      }

      return { success: true, method: addMethod, result };
    }

    return {
      success: false,
      error: '未找到可用的添加元素方法',
      debug: {
        elementType,
        panelMethods: panel ? Object.getOwnPropertyNames(panel) : null,
        templateMethods: Object.getOwnPropertyNames(template),
        panelExists: !!panel,
        panelType: typeof panel
      }
    };
  } catch (error) {
    console.error('添加元素时发生错误:', error);
    return { success: false, error: error.message, stack: error.stack };
  }
}

/**
 * 检查模板是否已完全初始化
 * @param {Object} template hiprint模板实例
 * @returns {boolean} 是否已初始化
 */
export function isTemplateReady(template) {
  if (!template) return false;

  // 检查基本方法是否存在
  const hasBasicMethods =
    typeof template.setPaper === 'function' &&
    typeof template.design === 'function';

  // 检查是否有编辑面板或面板数组
  const hasPanels =
    template.editingPanel || (template.panels && template.panels.length > 0);

  return hasBasicMethods && hasPanels;
}

/**
 * 等待模板初始化完成
 * @param {Object} template hiprint模板实例
 * @param {number} timeout 超时时间(ms)
 * @returns {Promise} 初始化结果
 */
export function waitForTemplateReady(template, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const checkReady = () => {
      if (isTemplateReady(template)) {
        resolve(template);
        return;
      }

      if (Date.now() - startTime > timeout) {
        reject(new Error('模板初始化超时'));
        return;
      }

      setTimeout(checkReady, 100);
    };

    checkReady();
  });
}

/**
 * 重置 hiprint 状态
 */
export function resetHiprint() {
  hiprintInstance = null;
  isInitialized = false;
}

/**
 * 设置元素选中回调
 * @param {Function} callback 回调函数
 */
export function setElementSelectCallback(callback) {
  designerConfig.onElementSelect = callback;
}

/**
 * 设置元素属性变更回调
 * @param {Function} callback 回调函数
 */
export function setElementChangeCallback(callback) {
  designerConfig.onElementChange = callback;
}

/**
 * 清理hiprint模板实例
 * @param {Object} template 模板实例
 * @param {string} containerSelector 容器选择器
 */
export function cleanupTemplate(
  template,
  containerSelector = '#hiprint-panel-center'
) {
  if (!template) return;

  try {
    // 清理设计器DOM
    const designerContainer = document.querySelector(containerSelector);
    if (designerContainer) {
      designerContainer.innerHTML = '';
      console.log('设计器DOM已清理');
    }

    // 清理可能的事件监听器
    if (template.panels && Array.isArray(template.panels)) {
      template.panels.forEach((panel) => {
        if (panel.printElements && Array.isArray(panel.printElements)) {
          panel.printElements.forEach((element) => {
            if (element.target && element.target.remove) {
              try {
                element.target.remove();
              } catch (e) {
                console.warn('清理元素DOM失败:', e);
              }
            }
          });
        }
      });
    }

    console.log('模板实例已清理');
  } catch (error) {
    console.warn('清理模板实例时发生错误:', error);
  }
}

/**
 * 更新元素属性
 * @param {Object} element 元素实例
 * @param {string} property 属性名
 * @param {any} value 属性值
 */
export function updateElementProperty(element, property, value) {
  if (!element || !element.options) {
    console.warn('无效的元素或元素选项');
    return false;
  }

  try {
    // 更新元素属性
    element.options[property] = value;

    // 如果元素有更新方法，调用它
    if (typeof element.update === 'function') {
      element.update();
    } else if (typeof element.refresh === 'function') {
      element.refresh();
    }

    // 触发变更回调
    if (
      designerConfig.onElementChange &&
      typeof designerConfig.onElementChange === 'function'
    ) {
      designerConfig.onElementChange(element, property, value);
    }

    console.log(`元素属性已更新: ${property} = ${value}`);
    return true;
  } catch (error) {
    console.error('更新元素属性失败:', error);
    return false;
  }
}

/**
 * 复制元素
 * @param {Object} template 模板实例
 * @param {Object} element 要复制的元素
 */
export function copyElement(template, element) {
  if (!template || !element) {
    console.warn('模板或元素不存在');
    return null;
  }

  try {
    // 创建元素副本
    const elementCopy = JSON.parse(JSON.stringify(element));

    // 调整位置避免重叠
    if (elementCopy.options) {
      elementCopy.options.left = (elementCopy.options.left || 0) + 20;
      elementCopy.options.top = (elementCopy.options.top || 0) + 20;
    }

    // 生成新的ID
    elementCopy.tid =
      'elem_' + Date.now() + '_' + Math.floor(Math.random() * 1000);

    // 添加到模板
    const result = safeAddElement(template, elementCopy);

    if (result.success) {
      console.log('元素复制成功');
      return result.result;
    } else {
      console.error('元素复制失败:', result.error);
      return null;
    }
  } catch (error) {
    console.error('复制元素时发生错误:', error);
    return null;
  }
}

/**
 * 删除元素
 * @param {Object} template 模板实例
 * @param {Object} element 要删除的元素
 */
export function deleteElement(template, element) {
  if (!template || !element) {
    console.warn('模板或元素不存在');
    return false;
  }

  try {
    // 查找元素所在的面板
    let targetPanel = null;
    let elementIndex = -1;

    if (template.panels && Array.isArray(template.panels)) {
      for (const panel of template.panels) {
        if (panel.printElements && Array.isArray(panel.printElements)) {
          elementIndex = panel.printElements.findIndex(
            (el) => el === element || el.tid === element.tid
          );
          if (elementIndex !== -1) {
            targetPanel = panel;
            break;
          }
        }
      }
    }

    if (targetPanel && elementIndex !== -1) {
      // 从面板中移除元素
      targetPanel.printElements.splice(elementIndex, 1);

      // 如果元素有DOM节点，移除它
      if (element.target && element.target.remove) {
        element.target.remove();
      }

      // 触发删除回调
      if (
        designerConfig.onElementDelete &&
        typeof designerConfig.onElementDelete === 'function'
      ) {
        designerConfig.onElementDelete(element);
      }

      console.log('元素删除成功');
      return true;
    } else {
      console.warn('未找到要删除的元素');
      return false;
    }
  } catch (error) {
    console.error('删除元素时发生错误:', error);
    return false;
  }
}

export default {
  initHiprint,
  getHiprintInstance,
  isHiprintInitialized,
  createPrintTemplate,
  createPreviewTemplate,
  getTemplatePaperInfo,
  detectPaperTypeAndOrientation,
  detectHiprintAPI,
  buildDraggableElements,
  safeAddElement,
  designerConfig,
  previewConfig,
  printConfig,
  paperSizes,
  setPaperSize,
  safeSetPaperSize,
  isTemplateReady,
  waitForTemplateReady,
  elementTemplates,
  fieldTypeMap,
  getFieldElementTemplate,
  handleHiprintError,
  resetHiprint,
  setElementSelectCallback,
  setElementChangeCallback,
  updateElementProperty,
  copyElement,
  deleteElement,
  cleanupTemplate,
  // 新增的统一打印配置函数 @since 2025-01-13
  createUnifiedPrintOptions,
  createUnifiedPreviewOptions,
  executeUnifiedPrint,
  executeUnifiedPreview
};
