[2m2025-08-03 12:13:50.483[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-03 12:13:50.754[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 97878 (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes started by paynexc in /Users/<USER>/project/ele-admin)
[2m2025-08-03 12:13:50.754[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-03 12:13:50.755[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-03 12:13:50.826[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/oraclepki.jar
[2m2025-08-03 12:13:50.827[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-03 12:13:50.827[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-03 12:13:52.408[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-03 12:13:52.412[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-03 12:13:52.533[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 102 ms. Found 0 JPA repository interfaces.
[2m2025-08-03 12:13:52.544[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-03 12:13:52.544[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-03 12:13:52.563[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 17 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-03 12:13:52.575[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-03 12:13:52.576[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-03 12:13:52.607[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
[2m2025-08-03 12:13:55.395[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 7070 (http)
[2m2025-08-03 12:13:55.442[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-7070"]
[2m2025-08-03 12:13:55.445[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-03 12:13:55.446[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-03 12:13:55.530[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-03 12:13:55.530[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 4703 ms
[2m2025-08-03 12:13:56.206[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='mongo_txRpa7', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@649a76c1], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@6bbd645, com.mongodb.Jep395RecordCodecProvider@4c97e3d5, com.mongodb.KotlinCodecProvider@672c6b93]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[paynexc.home:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@2338c3f9], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-03 12:13:56.234[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=20853000, minRoundTripTimeNanos=0}
[2m2025-08-03 12:13:56.521[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-03 12:13:56.723[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-03 12:13:56.727[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.payne.core.handler.DecryptInterceptor@2919976e'
[2m2025-08-03 12:13:56.727[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@127e838b'
[2m2025-08-03 12:13:56.727[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@83ec0b0'
[2m2025-08-03 12:13:57.043[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformItemMapper.xml]'
[2m2025-08-03 12:13:57.112[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformMapper.xml]'
[2m2025-08-03 12:13:57.157[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-03 12:13:57.187[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-03 12:13:57.223[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-03 12:13:57.254[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-03 12:13:57.302[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-03 12:13:57.338[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-03 12:13:57.390[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-03 12:13:57.424[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-03 12:13:57.446[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-03 12:13:57.473[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-03 12:13:57.518[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-03 12:13:57.533[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:15 workerId:23
[2m2025-08-03 12:13:57.851[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-03 12:13:57.979[0;39m [31mERROR[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-03 12:13:58.159[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[isson-netty-1-7][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for paynexc.home/************:6379
[2m2025-08-03 12:13:58.409[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[sson-netty-1-20][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for paynexc.home/************:6379
[2m2025-08-03 12:13:58.548[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.p.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-03 12:13:58.712[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-03 12:13:58.772[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-03 12:13:58.826[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-03 12:13:59.101[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-03 12:13:59.219[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-03 12:13:59.793[0;39m [33m WARN[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-03 12:13:59.829[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-03 12:13:56",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1651322523, ConnectTime:"2025-08-03 12:13:59", UseCount:1, LastActiveTime:"2025-08-03 12:13:59"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-03 12:14:00.931[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-03 12:14:00.939[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-03 12:14:01.898[0;39m [33m WARN[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-03 12:14:02.324[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-08-03 12:14:02.368[0;39m [33m WARN[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: a9f86965-4128-4e2b-8658-02f29f199be2

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-03 12:14:02.378[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-03 12:14:02.806[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-08-03 12:14:03.069[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: a9f86965-4128-4e2b-8658-02f29f199be2

[2m2025-08-03 12:14:03.209[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-7070"]
[2m2025-08-03 12:14:03.247[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 7070 (http) with context path '/'
[2m2025-08-03 12:14:03.261[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Started App in 13.449 seconds (process running for 15.802)
[2m2025-08-03 12:14:03.270[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-03 12:14:03.270[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-03 12:49:14.361[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-03 12:49:14.362[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-03 12:49:14.371[0;39m [32m INFO[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 8 ms
[2m2025-08-03 12:49:15.505[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-03 12:49:15.570[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-03 12:49:15.574[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-03 12:49:15.797[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-03 12:49:15.842[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-03 12:49:15.896[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-03 12:49:15.932[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-03 12:49:15.939[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-03 12:49:15.940[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-03 12:49:15.940[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-03 12:49:15.942[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-08-03 12:49:15.953[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-08-03 12:49:20.691[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-03 12:49:20.698[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-03 12:49:20.699[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-03 12:49:20.700[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-03 12:49:20.700[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-08-03 12:49:20.717[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-03 12:49:20.719[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT COUNT( * ) AS total FROM  LABEL_TEMPLATE         WHERE  (TEMPLATE_NAME = ? AND STATUS = ?)
[2m2025-08-03 12:49:20.726[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT( * ) AS total FROM  LABEL_TEMPLATE         WHERE  (TEMPLATE_NAME = ? AND STATUS = ?)
[2m2025-08-03 12:49:20.728[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) AS total FROM LABEL_TEMPLATE WHERE (TEMPLATE_NAME = ? AND STATUS = ?)
[2m2025-08-03 12:49:20.728[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectCount                 [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM LABEL_TEMPLATE WHERE (TEMPLATE_NAME = ? AND STATUS = ?)
[2m2025-08-03 12:49:20.729[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectCount                 [0;39m [2m:[0;39m ==> Parameters: 大签_副本(String), ACTIVE(String)
[2m2025-08-03 12:49:20.751[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectCount                 [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-03 12:49:20.756[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.LabelTemplateMapper.insert    [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO LABEL_TEMPLATE ( ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[2m2025-08-03 12:49:20.760[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.LabelTemplateMapper.insert    [0;39m [2m:[0;39m ==> Parameters: 5ff23ef38f2ba37a292e3804163205c6(String), 大签_副本(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":26,"width":191,"paperHeader":0,"paperFooter":73.70078740157481,"printElements":[{"options":{"left":363,"top":1.5,"height":46.5,"width":88.5,"title":"品相打分","right":447.75,"bottom":47.25,"vCenter":405,"hCenter":24,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"gradeScoreValue","hideTitle":true,"testData":"68","fontFamily":"Arial, sans-serif; font-weight: bold","fontSize":44,"fontWeight":"bolder","letterSpacing":1.5,"textAlign":"center","textContentVerticalAlign":"middle"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":465,"top":6,"height":49,"width":65,"title":"二维码","qrcodeType":"qrcode","right":530.75,"bottom":55,"vCenter":498.25,"hCenter":30.5,"qrCodeLevel":0,"coordinateSync":false,"widthHeightSync":false,"field":"qrcode","testData":"qrcode","hideTitle":true},"printElementType":{"title":"二维码","type":"qrcode"}},{"options":{"left":112.5,"top":6,"height":18,"width":147,"title":"银行名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":261.75,"bottom":21.75,"vCenter":188.25,"hCenter":13.5,"field":"bankName","fontFamily":"STKaiti","fontSize":16,"hideTitle":true,"testData":"中国人民银行","letterSpacing":1.5},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":456,"top":15,"height":40.5,"width":9,"title":"特殊标记","right":458.25,"bottom":52.5,"vCenter":453.75,"hCenter":32.25,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"specialMark","hideTitle":true,"testData":"EPQ","fontFamily":"SimSun"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":112.5,"top":25.5,"height":18,"width":145.5,"title":"钱币名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"coinName1","hideTitle":true,"testData":"拾元","fontFamily":"STKaiti","letterSpacing":0.75,"right":258,"bottom":43.5,"vCenter":185.25,"hCenter":36,"fontSize":12},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":132,"top":45,"height":15,"width":126,"title":"钱币编号-版别","right":258,"bottom":60,"vCenter":195,"hCenter":52.5,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"serialNumberWithVersion","hideTitle":true,"testData":"FZ-074779901-民族人物头像","fontFamily":"STKaiti","fontSize":10,"fontWeight":"bold"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":112.5,"top":45,"height":15,"width":19.5,"title":"S/N","right":129,"bottom":57.75,"vCenter":119.25,"hCenter":51,"coordinateSync":false,"widthHeightSync":false,"fontFamily":"STKaiti","qrCodeLevel":0,"fontSize":10},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":363,"top":49.5,"height":12,"width":90,"title":"品相","right":443.25,"bottom":63,"vCenter":402.75,"hCenter":57,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"gradeScore","hideTitle":true,"testData":"Superb Gem Unc68","fontFamily":"Arial","fontSize":10},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":471,"top":55.5,"height":13.5,"width":60,"title":"送评条码","field":"diyCode","coordinateSync":false,"widthHeightSync":false,"hideTitle":true,"qrCodeLevel":0,"right":530.25,"bottom":68.25,"vCenter":500.25,"hCenter":61.5,"fontFamily":"STKaiti","fontSize":10,"fontWeight":"bold"},"printElementType":{"title":"文本","type":"text"}}],"paperNumberLeft":388.5,"paperNumberTop":51,"paperNumberDisabled":true,"paperNumberContinue":true,"watermarkOptions":{"content":"","fillStyle":"rgba(184, 184, 184, 0.3)","fontSize":"14px","rotate":25,"width":200,"height":200,"timestamp":false,"format":"YYYY-MM-DD HH:mm"},"panelLayoutOptions":{"layoutType":"column","layoutRowGap":0,"layoutColumnGap":0}}]}(String), {"paperWidth":192,"paperHeight":290,"paperType":"custom","timestamp":"2025-08-02T08:33:04.768Z"}(String), false(Boolean), system(String), 2025-08-03T12:49:20.752419(LocalDateTime), 2025-08-03T12:49:20.755043(LocalDateTime), ACTIVE(String), 复制自：大签(String)
[2m2025-08-03 12:49:20.785[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.LabelTemplateMapper.insert    [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-03 12:49:20.827[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-03 12:49:20.834[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-03 12:49:20.836[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-03 12:49:20.836[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-03 12:49:20.836[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-03 12:49:20.850[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-03 12:50:41.297[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,   PAGE_SETTINGS=?, IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?, DESCRIPTION=?  WHERE ID=?
[2m2025-08-03 12:50:41.308[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,   PAGE_SETTINGS=?, IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?, DESCRIPTION=?  WHERE ID=?
[2m2025-08-03 12:50:41.310[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, PAGE_SETTINGS = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ?, DESCRIPTION = ? WHERE ID = ?
[2m2025-08-03 12:50:41.310[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, PAGE_SETTINGS = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ?, DESCRIPTION = ? WHERE ID = ?
[2m2025-08-03 12:50:41.312[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":26,"width":191,"paperHeader":0,"paperFooter":73.70078740157481,"printElements":[{"options":{"left":363,"top":1.5,"height":46.5,"width":88.5,"title":"品相打分","right":447.75,"bottom":47.25,"vCenter":405,"hCenter":24,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"gradeScoreValue","hideTitle":true,"testData":"68","fontFamily":"Arial, sans-serif; font-weight: bold","fontSize":44,"fontWeight":"bolder","letterSpacing":1.5,"textAlign":"center","textContentVerticalAlign":"middle"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":465,"top":6,"height":49,"width":65,"title":"二维码","qrcodeType":"qrcode","right":530.75,"bottom":55,"vCenter":498.25,"hCenter":30.5,"qrCodeLevel":0,"coordinateSync":false,"widthHeightSync":false,"field":"qrcode","testData":"qrcode","hideTitle":true},"printElementType":{"title":"二维码","type":"qrcode"}},{"options":{"left":112.5,"top":6,"height":18,"width":147,"title":"银行名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":261.75,"bottom":21.75,"vCenter":188.25,"hCenter":13.5,"field":"bankName","fontFamily":"STKaiti","fontSize":16,"hideTitle":true,"testData":"中国人民银行","letterSpacing":1.5},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":456,"top":15,"height":40.5,"width":9,"title":"特殊标记","right":458.25,"bottom":52.5,"vCenter":453.75,"hCenter":32.25,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"specialMark","hideTitle":true,"testData":"EPQ","fontFamily":"SimSun"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":112.5,"top":25.5,"height":18,"width":145.5,"title":"钱币名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"coinName1","hideTitle":true,"testData":"拾元","fontFamily":"STKaiti","letterSpacing":0.75,"right":258,"bottom":43.5,"vCenter":185.25,"hCenter":36,"fontSize":12},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":132,"top":45,"height":15,"width":126,"title":"钱币编号-版别","right":258,"bottom":60,"vCenter":195,"hCenter":52.5,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"serialNumberWithVersion","hideTitle":true,"testData":"FZ-074779901-民族人物头像","fontFamily":"STKaiti","fontSize":10,"fontWeight":"bold"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":112.5,"top":45,"height":15,"width":19.5,"title":"S/N","right":129,"bottom":57.75,"vCenter":119.25,"hCenter":51,"coordinateSync":false,"widthHeightSync":false,"fontFamily":"STKaiti","qrCodeLevel":0,"fontSize":10},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":363,"top":49.5,"height":12,"width":90,"title":"品相","right":443.25,"bottom":63,"vCenter":402.75,"hCenter":57,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"gradeScore","hideTitle":true,"testData":"Superb Gem Unc68","fontFamily":"Arial","fontSize":10},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":471,"top":55.5,"height":13.5,"width":60,"title":"送评条码","field":"diyCode","coordinateSync":false,"widthHeightSync":false,"hideTitle":true,"qrCodeLevel":0,"right":530.25,"bottom":68.25,"vCenter":500.25,"hCenter":61.5,"fontFamily":"STKaiti","fontSize":10,"fontWeight":"bold"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":0,"top":0,"height":70.5,"width":540,"fit":"","coordinateSync":false,"widthHeightSync":false,"right":537.75,"bottom":58.5,"vCenter":267.75,"hCenter":23.25},"printElementType":{"title":"图片","type":"image"}}],"paperNumberLeft":388.5,"paperNumberTop":51,"paperNumberDisabled":true,"paperNumberContinue":true,"watermarkOptions":{"content":"","fillStyle":"rgba(184, 184, 184, 0.3)","fontSize":"14px","rotate":25,"width":200,"height":200,"timestamp":false,"format":"YYYY-MM-DD HH:mm"},"panelLayoutOptions":{"layoutType":"column","layoutRowGap":0,"layoutColumnGap":0}}]}(String), {"paperWidth":192,"paperHeight":290,"paperType":"custom","timestamp":"2025-08-03T04:50:41.117Z"}(String), false(Boolean), 2025-08-03T12:50:41.268297(LocalDateTime), ACTIVE(String), (String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-08-03 12:50:41.344[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-03 12:50:41.409[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-03 12:50:41.416[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-03 12:50:41.418[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-03 12:50:41.419[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-03 12:50:41.419[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-03 12:50:41.454[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-03 12:50:50.556[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYS_PARAM
[2m2025-08-03 12:50:50.561[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYS_PARAM
[2m2025-08-03 12:50:50.563[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYS_PARAM
[2m2025-08-03 12:50:50.573[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYS_PARAM
[2m2025-08-03 12:50:50.573[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-03 12:50:50.658[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-03 12:50:50.673[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYS_PARAM ORDER BY create_time DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-03 12:50:50.674[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-03 12:50:50.693[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-03 12:51:13.296[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYS_PARAM         WHERE  (PARAM_NAME = ?)
[2m2025-08-03 12:51:13.304[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYS_PARAM         WHERE  (PARAM_NAME = ?)
[2m2025-08-03 12:51:13.306[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYS_PARAM WHERE (PARAM_NAME = ?)
[2m2025-08-03 12:51:13.306[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYS_PARAM WHERE (PARAM_NAME = ?)
[2m2025-08-03 12:51:13.307[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: 模板底图(String)
[2m2025-08-03 12:51:13.331[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-03 12:51:13.335[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.mapper.SysParamMapper.insert    [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO SYS_PARAM ( ID, PARAM_NAME, PARAM_MODE, create_time ) VALUES ( ?, ?, ?, ? )
[2m2025-08-03 12:51:13.335[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.mapper.SysParamMapper.insert    [0;39m [2m:[0;39m ==> Parameters: 0ead34560494a66b4633e450cd554e48(String), 模板底图(String), hiprint(String), 2025-08-03T12:51:13.332657(LocalDateTime)
[2m2025-08-03 12:51:13.366[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.mapper.SysParamMapper.insert    [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-03 12:51:13.476[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYS_PARAM
[2m2025-08-03 12:51:13.482[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYS_PARAM
[2m2025-08-03 12:51:13.482[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYS_PARAM
[2m2025-08-03 12:51:13.493[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYS_PARAM
[2m2025-08-03 12:51:13.494[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-03 12:51:13.507[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-03 12:51:13.515[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYS_PARAM ORDER BY create_time DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-03 12:51:13.516[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-03 12:51:13.545[0;39m [32mDEBUG[0;39m [35m97878[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m <==      Total: 2
