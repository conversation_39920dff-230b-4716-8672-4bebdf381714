<template>
  <div class="coin-detail-table">
    <el-table
      :data="computedTableData"
      border
      stripe
      size="small"
      max-height="500"
      empty-text="暂无数据"
      :row-class-name="getRowClassName"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="60" align="center" />

      <!-- 钱币编号 -->
      <el-table-column
        prop="nummber"
        label="钱币编号"
        width="130"
        align="center"
      >
        <template #default="{ row }">
          <span
            style="font-family: monospace; font-weight: bold; color: #409eff"
          >
            {{ row.nummber }}
          </span>
        </template>
      </el-table-column>

      <!-- 钱币名称1 -->
      <el-table-column prop="coinName1" label="名称1" width="120">
        <template #default="{ row }">
          <el-input
            v-model="row.coinName1"
            placeholder="名称1"
            size="small"
            @change="handleFieldChange(row, 'coinName1')"
          />
        </template>
      </el-table-column>

      <!-- 钱币名称2 -->
      <el-table-column prop="coinName2" label="名称2" width="120">
        <template #default="{ row }">
          <el-input
            v-model="row.coinName2"
            placeholder="名称2"
            size="small"
            @change="handleFieldChange(row, 'coinName2')"
          />
        </template>
      </el-table-column>

      <!-- 年代 -->
      <el-table-column prop="yearInfo" label="年代" width="100">
        <template #default="{ row }">
          <el-input
            v-model="row.yearInfo"
            placeholder="年代"
            size="small"
            @change="handleFieldChange(row, 'yearInfo')"
          />
        </template>
      </el-table-column>
      <!-- 纸币专用字段 -->
      <template v-if="isBanknote">
        <!-- 目录 -->
        <el-table-column prop="catalog" label="目录" width="120">
          <template #default="{ row }">
            <el-input
              v-model="row.catalog"
              placeholder="目录"
              size="small"
              @change="handleFieldChange(row, 'catalog')"
            />
          </template>
        </el-table-column>

        <!-- 银行名称 -->
        <el-table-column prop="bankName" label="银行名称" width="140">
          <template #default="{ row }">
            <el-input
              v-model="row.bankName"
              placeholder="银行名称"
              size="small"
              @change="handleFieldChange(row, 'bankName')"
            />
          </template>
        </el-table-column>
      </template>

      <!-- 古钱币专用字段 -->
      <template v-if="isAncientCoin">
        <!-- 材质 -->
        <el-table-column prop="material" label="材质" width="100">
          <template #default="{ row }">
            <dict-data
              v-model="row.material"
              code="material_ancient"
              type="select"
              placeholder="材质"
              size="small"
              style="width: 100%"
              @change="handleFieldChange(row, 'material')"
            />
          </template>
        </el-table-column>
      </template>

      <!-- 机制币专用字段 -->
      <template v-if="isMachineCoin">
        <!-- 面值 -->
        <el-table-column prop="faceValue" label="面值" width="100">
          <template #default="{ row }">
            <el-input
              v-model="row.faceValue"
              placeholder="面值"
              size="small"
              @change="handleFieldChange(row, 'faceValue')"
            />
          </template>
        </el-table-column>

        <!-- 材质 -->
        <el-table-column prop="material" label="材质" width="100">
          <template #default="{ row }">
            <dict-data
              v-model="row.material"
              code="material_machine"
              type="select"
              placeholder="材质"
              size="small"
              style="width: 100%"
              @change="handleFieldChange(row, 'material')"
            />
          </template>
        </el-table-column>

        <!-- 尺寸 -->
        <el-table-column prop="coinSize" label="尺寸" width="100">
          <template #default="{ row }">
            <el-input
              v-model="row.coinSize"
              placeholder="尺寸"
              size="small"
              @change="handleFieldChange(row, 'coinSize')"
            />
          </template>
        </el-table-column>

        <!-- 重量 -->
        <el-table-column prop="coinWeight" label="重量" width="100">
          <template #default="{ row }">
            <el-input
              v-model="row.coinWeight"
              placeholder="重量"
              size="small"
              @change="handleFieldChange(row, 'coinWeight')"
            />
          </template>
        </el-table-column>
      </template>

      <!-- 银锭专用字段 -->
      <template v-if="isSilverIngot">
        <!-- 年份 -->
        <el-table-column prop="year" label="年份" width="100">
          <template #default="{ row }">
            <el-input
              v-model="row.year"
              placeholder="年份"
              size="small"
              @change="handleFieldChange(row, 'year')"
            />
          </template>
        </el-table-column>

        <!-- 地区 -->
        <el-table-column prop="region" label="地区" width="100">
          <template #default="{ row }">
            <el-input
              v-model="row.region"
              placeholder="地区"
              size="small"
              @change="handleFieldChange(row, 'region')"
            />
          </template>
        </el-table-column>

        <!-- 税种 -->
        <el-table-column prop="taxType" label="税种" width="100">
          <template #default="{ row }">
            <el-input
              v-model="row.taxType"
              placeholder="税种"
              size="small"
              @change="handleFieldChange(row, 'taxType')"
            />
          </template>
        </el-table-column>

        <!-- 面值 -->
        <el-table-column prop="faceValue" label="面值" width="100">
          <template #default="{ row }">
            <el-input
              v-model="row.faceValue"
              placeholder="面值"
              size="small"
              @change="handleFieldChange(row, 'faceValue')"
            />
          </template>
        </el-table-column>

        <!-- 材质 -->
        <el-table-column prop="material" label="材质" width="100">
          <template #default="{ row }">
            <dict-data
              v-model="row.material"
              code="material_silver"
              type="select"
              placeholder="材质"
              size="small"
              style="width: 100%"
              @change="handleFieldChange(row, 'material')"
            />
          </template>
        </el-table-column>
      </template>

      <!-- 盒子类型 -->
      <el-table-column prop="boxType" label="盒子类型" width="100">
        <template #default="{ row }">
          <dict-data
            v-model="row.boxType"
            code="packType"
            type="select"
            placeholder="盒子类型"
            size="small"
            style="width: 100%"
            @change="handleFieldChange(row, 'boxType')"
          />
        </template>
      </el-table-column>

      <!-- 数量 -->
      <el-table-column prop="amount" label="数量" width="80">
        <template #default="{ row }">
          <el-input-number
            v-model="row.amount"
            :min="1"
            size="small"
            style="width: 100%"
            @change="handleFieldChange(row, 'amount')"
          />
        </template>
      </el-table-column>

      <!-- 标准价 -->
      <el-table-column prop="standardPrice" label="标准价" width="100">
        <template #default="{ row }">
          <el-input-number
            v-model="row.standardPrice"
            :min="0"
            :precision="2"
            size="small"
            style="width: 100%"
            @change="handleFieldChange(row, 'standardPrice')"
          />
        </template>
      </el-table-column>

      <!-- 国际价 -->
      <el-table-column prop="internationalPrice" label="国际价" width="100">
        <template #default="{ row }">
          <el-input-number
            v-model="row.internationalPrice"
            :min="0"
            :precision="2"
            size="small"
            style="width: 100%"
            @change="handleFieldChange(row, 'internationalPrice')"
          />
        </template>
      </el-table-column>

      <!-- 费用 -->
      <el-table-column prop="gradeFee" label="费用" width="100">
        <template #default="{ row }">
          <el-input-number
            v-model="row.gradeFee"
            :min="0"
            :precision="2"
            size="small"
            style="width: 100%"
            @change="handleFieldChange(row, 'gradeFee')"
          />
        </template>
      </el-table-column>

      <!-- 折扣 -->
      <el-table-column prop="discount" label="折扣" width="80">
        <template #default="{ row }">
          <el-input-number
            v-model="row.discount"
            :min="0"
            :max="100"
            :precision="1"
            size="small"
            style="width: 100%"
            @change="handleFieldChange(row, 'discount')"
          />
        </template>
      </el-table-column>

      <!-- 盒子费 -->
      <el-table-column prop="boxFee" label="盒子费" width="80">
        <template #default="{ row }">
          <el-input-number
            v-model="row.boxFee"
            :min="0"
            :precision="2"
            size="small"
            style="width: 100%"
            @change="handleFieldChange(row, 'boxFee')"
          />
        </template>
      </el-table-column>

      <!-- 加急费 -->
      <el-table-column prop="urgentFee" label="加急费" width="80">
        <template #default="{ row }">
          <el-input-number
            v-model="row.urgentFee"
            :min="0"
            :precision="2"
            size="small"
            style="width: 100%"
            @change="handleFieldChange(row, 'urgentFee')"
          />
        </template>
      </el-table-column>

      <!-- 古钱币专用字段 -->
      <template v-if="isAncientCoin">
        <!-- 等级 -->
        <el-table-column prop="rank" label="等级" width="100">
          <template #default="{ row }">
            <el-input
              v-model="row.rank"
              placeholder="等级"
              size="small"
              @change="handleFieldChange(row, 'rank')"
            />
          </template>
        </el-table-column>
      </template>
      <!-- 版别 -->
      <el-table-column prop="version" label="版别" width="150">
        <template #default="{ row }">
          <dict-data
            v-model="row.version"
            code="edition"
            type="select"
            placeholder="请选择版别"
            filterable
            clearable
            size="small"
            style="width: 100%"
            @change="handleFieldChange(row, 'version')"
          />
        </template>
      </el-table-column>

      <!-- 非纸币 对内备注 -->
      <template v-if="coinType !== 'banknote'">
        <!-- 对内备注 -->
        <el-table-column prop="internalNote" label="对内备注" width="150">
          <template #default="{ row }">
            <el-input
              v-model="row.internalNote"
              placeholder="对内备注"
              size="small"
              @change="handleFieldChange(row, 'internalNote')"
            />
          </template>
        </el-table-column>
      </template>

      <!-- 纸币特有字段 -->
      <template v-if="isBanknote">
        <!-- 特殊标签 -->
        <el-table-column prop="specialLabel" label="特殊标签" width="150">
          <template #default="{ row }">
            <el-input
              v-model="row.specialLabel"
              placeholder="特殊标签"
              size="small"
              @change="handleFieldChange(row, 'specialLabel')"
            />
          </template>
        </el-table-column>
        <!-- 内置公司 -->
        <!--        <el-table-column prop="company" label="内置公司" width="120">
          <template #default="{ row }">
            <el-select
              v-model="row.company"
              placeholder="内置公司"
              size="small"
              style="width: 100%"
              @change="handleFieldChange(row, 'company')"
            >
              <el-option label="中乾评级" :value="1" />
              <el-option label="宝鑫评级" :value="2" />
            </el-select>
          </template>
        </el-table-column>-->
      </template>

      <!-- 图片列 -->
      <el-table-column label="图片" width="150" align="center">
        <template #default="{ row }">
          <coin-image-upload
            v-model="row.coinImages"
            :limit="3"
            :show-title="false"
            :show-info="false"
            :item-style="{ width: '40px', height: '40px' }"
            :button-style="{ width: '40px', height: '40px' }"
            placeholder="上传"
            :max-size="5"
            @upload-success="(data) => handleImageChange(row, data, 'upload')"
            @remove-success="(data) => handleImageChange(row, data, 'remove')"
          />
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row, $index }">
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row, $index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import { defineComponent, ref, computed, onMounted } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import DictData from '@/components/DictData/index.vue';
  import CoinImageUpload from '@/components/CoinImageUpload/index.vue';
  import { useCoinTypes } from '@/composables/use-coin-types';

  export default defineComponent({
    name: 'CoinDetailTable',
    components: {
      DictData,
      CoinImageUpload
    },
    props: {
      tableData: {
        type: Array,
        default: () => []
      },
      coins: {
        type: Array,
        default: () => []
      },
      coinType: {
        type: String,
        default: 'banknote' // 默认为纸币，验证器已改为动态获取
      }
    },
    emits: ['update', 'delete', 'field-change'],
    setup(props, { emit }) {
      const selectedCoins = ref([]);

      // 使用动态代码表替代硬编码判断
      const {
        checkIsBanknote,
        checkIsAncientCoin,
        checkIsMachineCoin,
        checkIsSilverIngot
      } = useCoinTypes();

      // 动态类型判断计算属性
      const isBanknote = computed(() => checkIsBanknote(props.coinType));
      const isAncientCoin = computed(() => checkIsAncientCoin(props.coinType));
      const isMachineCoin = computed(() => checkIsMachineCoin(props.coinType));
      const isSilverIngot = computed(() => checkIsSilverIngot(props.coinType));

      // 表格数据
      const computedTableData = computed(() => {
        const data = props.tableData || props.coins || [];
        // 过滤掉被标记为删除的项目
        return data.filter((item) => !item._deleted);
      });

      // 选择变化
      const handleSelectionChange = (selection) => {
        selectedCoins.value = selection;
      };

      // 处理字段变化
      const handleFieldChange = (row, field) => {
        emit('field-change', { row, field });
      };

      // 删除钱币
      const handleDelete = async (row, index) => {
        try {
          await ElMessageBox.confirm(
            `确定要删除钱币编号为 "${row.nummber}" 的记录吗？`,
            '删除确认',
            {
              type: 'warning'
            }
          );

          emit('delete', { row, index });
          ElMessage.success('删除成功');
        } catch (error) {
          if (error !== 'cancel') {
            ElMessage.error('删除失败');
          }
        }
      };

      // 获取行样式类名
      const getRowClassName = ({ row, rowIndex }) => {
        if (row.isTemp) {
          return 'new-coin-row';
        }
        return '';
      };

      // 处理图片变更
      const handleImageChange = (row, data, action) => {
        console.log(`图片${action}:`, data);
        // 触发字段变更事件
        handleFieldChange(row, 'coinImages');
      };

      // 组件挂载时的初始化
      onMounted(() => {
        // dict-data组件会自动加载码表数据，无需手动调用
      });

      return {
        selectedCoins,
        computedTableData,
        handleSelectionChange,
        handleFieldChange,
        handleDelete,
        getRowClassName,
        handleImageChange,
        // 动态类型判断
        isBanknote,
        isAncientCoin,
        isMachineCoin,
        isSilverIngot
      };
    }
  });
</script>

<style scoped>
  .coin-detail-table {
    position: relative;
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-input__inner) {
    font-size: 12px;
  }

  /* 新添加行的高亮样式 */
  :deep(.new-coin-row) {
    background-color: #f0f9ff !important;
    border-left: 3px solid #409eff;
  }

  :deep(.new-coin-row:hover) {
    background-color: #e6f7ff !important;
  }

  /* 新添加行的动画效果 */
  :deep(.new-coin-row) {
    animation: highlight-new-row 2s ease-in-out;
  }

  @keyframes highlight-new-row {
    0% {
      background-color: #e6f7ff;
      transform: scale(1.01);
    }
    50% {
      background-color: #bae7ff;
    }
    100% {
      background-color: #f0f9ff;
      transform: scale(1);
    }
  }
</style>
